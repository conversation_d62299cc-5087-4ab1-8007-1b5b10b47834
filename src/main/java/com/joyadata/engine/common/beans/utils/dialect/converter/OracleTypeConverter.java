package com.joyadata.engine.common.beans.utils.dialect.converter;

import com.alibaba.fastjson.JSON;
import com.joyadata.engine.common.beans.dto.DdlDTO;
import com.joyadata.engine.common.beans.dto.datasource.MetadataColumnDTO;
import com.joyadata.engine.common.beans.utils.dialect.AbstractDatabaseTypeConverter;
import com.joyadata.engine.common.beans.utils.dialect.paramters.DDLParameters;
import groovy.lang.Tuple2;
import groovy.lang.Tuple3;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.sql.Types;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2025/4/25 17:46
 */
@Slf4j
public class OracleTypeConverter extends AbstractDatabaseTypeConverter {
    @Override
    protected Tuple3<String, String, String> doGenerateDDL(DDLParameters ddlParameters) {
        String schema = ddlParameters.getSchema();
        String tableName = ddlParameters.getTableName();
        List<DdlDTO> columns = ddlParameters.getColumns();
        String tableCnName = ddlParameters.getTableCnName();
        String fieldUppLower = ddlParameters.getFieldUppLower();
        if (StringUtils.isNotBlank(fieldUppLower)) {
            tableName = getUppOrLowerCase(fieldUppLower, tableName);
        }
        List<String> list = new ArrayList<>();
        String checkTableSql = "SELECT 1 FROM  \"" + tableName + "\" WHERE 1=2";
        String create = "CREATE TABLE \"" + tableName + "\" (";
        if (StringUtils.isNotBlank(schema)) {
            if (StringUtils.isNotBlank(fieldUppLower)) {
                schema = getUppOrLowerCase(fieldUppLower, schema);
            }
            create = "CREATE TABLE \"" + schema + "\".\"" + tableName + "\" (";
            checkTableSql = "SELECT 1 FROM  \"" + schema + "\".\"" + tableName + "\" WHERE 1=2";
        }
        StringBuilder ddl = new StringBuilder(create);
        String finalTableName = tableName;
        String finalSchema = schema;
        columns.forEach(ddlDTO -> {
            int dataType = ddlDTO.getDataTypeJava();// Integer.valueOf(column.get("data_type"));
            int columnSize = ddlDTO.getColumnSize();// Integer.valueOf(column.get("max_length"));
            int decimalDigits = ddlDTO.getDecimalDigits(); //Integer.valueOf(column.get("decimal_digits"));
            String dataName = ddlDTO.getDataType(); //column.get("type_name");
            //String columnName = ddlDTO.getColumnName().toUpperCase(); //column.get("column_name").toLowerCase();
            String columnName;
            if (StringUtils.isNotBlank(fieldUppLower)) {
                columnName = getUppOrLowerCase(fieldUppLower, ddlDTO.getColumnName());
            } else {
                columnName = ddlDTO.getColumnName().toUpperCase(); //column.get("column_name").toLowerCase();
            }
            String cnName = ddlDTO.getColumnCnName();// StringUtils.defaultString(column.get("remarks"),"");
            //int position = ddlDTO.getPosition();
            String dataTypeString = getOracleTypeFromSqlType(dataType, dataName, columnSize, decimalDigits);
            //log.info("当前字段名称是 {} ,字段javaType是 {} ,元数据名称是 {},转换为 {}", columnName, dataType, dataName, dataTypeString);
            ddl.append("\"").append(columnName).append("\" ").append(dataTypeString).append(",");
            if (StringUtils.isNotBlank(cnName)) {
                cnName = cnName.replaceAll("'", "`");
                cnName = cnName.replaceAll("\"", "“");
                if (StringUtils.isNotBlank(finalSchema)) {
                    list.add("COMMENT ON COLUMN \"" + finalSchema + "\".\"" + finalTableName + "\"." + columnName + " IS '" + cnName + "'");
                } else {
                    list.add("COMMENT ON COLUMN \"" + finalTableName + "\"." + columnName + " IS '" + cnName + "'");
                }
            }
            //comments.add();
        });
        //表中文名
        if (StringUtils.isNotBlank(tableCnName)) {
            tableCnName = tableCnName.replaceAll("'", "`");
            tableCnName = tableCnName.replaceAll("\"", "“");
            if (StringUtils.isNotBlank(schema)) {
                list.add("COMMENT ON TABLE  \"" + schema + "\".\"" + tableName + "\" IS '" + tableCnName + "'");
            } else {
                list.add("COMMENT ON TABLE  \"" + tableName + "\" IS '" + tableCnName + "'");
            }
        }
        String result = ddl.toString();
        result = result.substring(0, result.length() - 1);
        result = result + ")";
        //result=result+comments.stream().collect(Collectors.joining(""));
        log.info("OracleDDLFromMetadata生成Oracle建表语句是 {}", result);
        log.info("OracleDDLFromMetadata生成Oracle检测表是否存在的语句是 {}", checkTableSql);
        Tuple3<String, String, String> tuple3 = new Tuple3<>(result, JSON.toJSONString(list), checkTableSql);
        return tuple3;
    }

    private static String getOracleTypeFromSqlType(int dataType, String dataName, int columnSize, int decimalDigits) {
        switch (dataType) {
            case Types.BIT:
                return "CHAR(1)";
            case Types.BOOLEAN:
                return "NUMBER(1)";
            case Types.TINYINT:
            case Types.SMALLINT:
            case Types.INTEGER:
                if (0 == columnSize) {
                    return "NUMBER";
                }
                return "NUMBER(" + columnSize + ")";
            case Types.BIGINT:
                return "NUMBER(" + columnSize + ", 0)";
            case Types.REAL:
            case Types.FLOAT:
            case Types.DOUBLE:
                if ("float4".equalsIgnoreCase(dataName)) { //pg处理
                    return "BINARY_FLOAT";
                } else if ("float8".equalsIgnoreCase(dataName)) { //pg处理
                    return "BINARY_DOUBLE";
                } else if ("float".equalsIgnoreCase(dataName)) {//sqlserver处理
                    if (columnSize == 24) {
                        return "BINARY_FLOAT";
                    } else if (columnSize == 53) {
                        return "BINARY_DOUBLE";
                    } else {
                        return "FLOAT";
                    }
                } else if ("DOUBLE PRECISION".equalsIgnoreCase(dataName)) {//达梦数据库
                    return "BINARY_DOUBLE";
                }
                return "NUMBER(" + columnSize + ", " + decimalDigits + ")";
            case Types.NUMERIC:
            case Types.DECIMAL:
                if ((columnSize == 0 && decimalDigits == -127) || columnSize >= 38 || (columnSize == 0 && decimalDigits == 0)) {
                    return "NUMBER";
                } else {
                    return "NUMBER(" + columnSize + ", " + decimalDigits + ")";
                }
            case Types.CHAR:
            case Types.NCHAR:
                return "CHAR(" + columnSize + ")";
            case Types.VARCHAR:
                if (columnSize > 20000 || columnSize == 0 || "text".equalsIgnoreCase(dataName)) {
                    return "CLOB";
                } else if (columnSize > 4000) {
                    return "VARCHAR2(4000)";
                } else {
                    return "VARCHAR2(" + columnSize + ")";
                }
            case Types.NVARCHAR:
                if (columnSize > 2000) {
                    return "VARCHAR2(" + columnSize + ")";
                } else if (columnSize == 0) {
                    return "CLOB";
                } else {
                    return "NVARCHAR2(" + columnSize + ")";
                }
            case Types.LONGVARCHAR:
            case Types.LONGNVARCHAR:
                if ("long".equalsIgnoreCase(dataName)) { //ORACLE Long 类型
                    return "LONG";
                }
                //mysql到oracle是mysql是text类型  datyType等于-1  使用clob类型
                return "CLOB";

            case Types.DATE:
                return "DATE";
            case Types.TIME:
                return "TIMESTAMP";
            case Types.TIMESTAMP:
                return "TIMESTAMP(" + decimalDigits + ")";
            case Types.BLOB:
            case Types.BINARY:
                return "BLOB";
            case Types.CLOB:
                return "CLOB";
            default:
                if (StringUtils.isNotBlank(dataName)) {
                    if ("NVARCHAR2".equalsIgnoreCase(dataName)) {
                        return "VARCHAR2(" + columnSize + ")";
                    } else if ("VARCHAR2".equalsIgnoreCase(dataName)) {
                        return "VARCHAR2(" + columnSize + ")";
                    } else if (dataName.startsWith("TIMESTAMP")) {
                        return "TIMESTAMP(" + decimalDigits + ")";
                    } else if ("NCHAR".equalsIgnoreCase(dataName)) {
                        return "NCHAR(" + columnSize + ")";
                    } else if ("NCLOB".equalsIgnoreCase(dataName)) {
                        return "NCLOB";
                    } else if ("BINARY_FLOAT".equalsIgnoreCase(dataName)) {
                        return "BINARY_FLOAT";
                    } else if ("BINARY_DOUBLE".equalsIgnoreCase(dataName)) {
                        return "BINARY_DOUBLE";
                    } else if ("NTERVAL YEAR".equalsIgnoreCase(dataName)) {
                        return "NTERVAL YEAR(" + columnSize + ")";
                    } else if ("BFILE".equalsIgnoreCase(dataName)) {
                        return "VARCHAR2(4000)";
                    } else if ("ROWID".equalsIgnoreCase(dataName)) {
                        return "ROWID";
                    } else if ("UROWID".equalsIgnoreCase(dataName)) {
                        return "UROWID";
                    } else if ("RAW".equalsIgnoreCase(dataName)) {
                        return "RAW(" + columnSize + ")";
                    } else if ("INTERVAL YEAR(2) TO MONTH".equalsIgnoreCase(dataName)) {
                        return "VARCHAR(10)";
                    } else if (dataName.startsWith("TIMESTAMP")) {
                        return "TIMESTAMP";
                    } else if (dataName.equalsIgnoreCase("serial")) {//pg数据库转int
                        return "NUMBER(10)";
                    }
                }
                return "VARCHAR2(4000)";

        }
    }

    @Override
    public Tuple2<String, List<String>> generateAddPkSql(String schema, String tableName, List<MetadataColumnDTO> fieldList, String fieldUppLower) {
        List<String> pkFieldList = fieldList.stream().map(MetadataColumnDTO::getColumnName).collect(Collectors.toList());
        if (StringUtils.isNotBlank(fieldUppLower)) {
            tableName = getUppOrLowerCase(fieldUppLower, tableName);
            pkFieldList = convertListCase(pkFieldList, fieldUppLower);
        }
        String pkField = pkFieldList.stream()
                .map(str -> "\"" + str + "\"") // 在每个元素前后添加双引号
                .collect(Collectors.joining(","));
        String pkSql = "ALTER  TABLE  \"" + tableName + "\" ADD PRIMARY KEY (" + pkField + ")";
        if (StringUtils.isNotBlank(schema)) {
            if (StringUtils.isNotBlank(fieldUppLower)) {
                schema = getUppOrLowerCase(fieldUppLower, schema);
            }
            pkSql = "ALTER  TABLE \"" + schema + "\".\"" + tableName + "\" ADD PRIMARY KEY (" + pkField + ")";
        }
        Tuple2<String, List<String>> tuple2 = new Tuple2<>(pkSql, null);
        return tuple2;
    }
}
