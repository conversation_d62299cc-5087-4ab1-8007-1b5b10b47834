package com.joyadata.engine.common.beans.utils.dialect;

import com.joyadata.engine.common.beans.utils.dialect.paramters.DDLParameters;
import groovy.lang.Tuple3;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.stream.Collectors;

@Slf4j
public abstract class AbstractDatabaseTypeConverter implements DatabaseTypeConverter {
    @Override
    public Tuple3<String, String, String> generateDDL(DDLParameters ddlParameters) {
        return doGenerateDDL(ddlParameters);
    }

    protected abstract Tuple3<String, String, String> doGenerateDDL(DDLParameters ddlParameters);
    
    public static String getUppOrLowerCase(String fieldUppLower, String columnName) {
        return columnName;
    }

    public static List<String> convertListCase(List<String> pkFieldList, String fieldUppLower) {
        return pkFieldList.stream()
                .map(str -> getUppOrLowerCase(fieldUppLower, str))
                .collect(Collectors.toList());
    }

    protected String tablePath(String schema, String tableName){
        if (StringUtils.isEmpty(schema)) {
            return qute() + tableName + qute();
        } else {
            return qute() + schema + qute() + "." + qute() + tableName + qute();
        }
    }

    protected String quoteIdentifier(String var1){
        return qute() + var1 + qute();
    }

    protected String qute(){
        return "";
    }

}