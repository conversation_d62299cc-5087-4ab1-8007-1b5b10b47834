package com.joyadata.engine.common.beans.dto.engine.sink;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.*;

@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class EngineHttpBaseSinkModel implements Serializable {

    private String url;

    private String method = "POST";

    /**
     * http sink 属性
     */
    Map<String, String> params = new HashMap<>();
    Map<String, String> headers = new HashMap<>();
    List<LinkedHashMap<String, String>> sinkOutputColumns = new ArrayList<>();


    /**
     * 发送方式1： form-data
     * 发送方式2： x-www-form-urlencoded
     * 发送方式3： raw
     */
    private String bodySendType = "raw";
    private int connectTimeoutMs = 15;


    private String datetimeFormat = "yyyy-MM-dd HH:mm:ss";

    /**
     * YYYY_MM_DD("yyyy-MM-dd"),
     * YY_MM_DD("yy-MM-dd"),
     * YYYY_MM_DD_SPOT("yyyy.MM.dd"),
     * YYYY_MM_DD_SLASH("yyyy/MM/dd");
     */
    private String dateFormat = "yyyy-MM-dd";
    /**
     * HH_MM_SS("HH:mm:ss"),
     * HH_MM_SS_SSS("HH:mm:ss.SSS");
     */
    private String timeFormat = "HH:mm:ss";


    /**
     * 当source_table_name未指定时，当前插件处理配置文件中前一个插件输出的dataset数据集。当指定source_table_name时，当前插件处理的是对应此参数的数据集。
     */
    private String sourceTableName;

}
