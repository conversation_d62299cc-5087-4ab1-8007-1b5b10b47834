package com.joyadata.engine.common.beans.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version V1.0
 * @ClassName: DdlDTO
 * @date 2024/2/23
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class DdlDTO {
    private String columnName;
    private String columnCnName;
    private String dataType;
    private int dataTypeJava;
    private int columnSize;
    private int decimalDigits;
    private int position;
    private int primaryKey;//是否主键，1是，0不是
    private int partition;//是否是分区字段，1是，0不是
    private int orderNum; // 字段排序
}
