package com.joyadata.engine.common.beans.utils.dialect.converter;

import com.joyadata.engine.common.beans.dto.DdlDTO;
import com.joyadata.engine.common.beans.dto.datasource.MetadataColumnDTO;
import com.joyadata.engine.common.beans.utils.dialect.AbstractDatabaseTypeConverter;
import com.joyadata.engine.common.beans.utils.dialect.paramters.DDLParameters;
import groovy.lang.Tuple2;
import groovy.lang.Tuple3;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2025/7/29 18:05
 */
@Slf4j
public class MaxComputeTypeConverter extends AbstractDatabaseTypeConverter {
    @Override
    protected Tuple3<String, String, String> doGenerateDDL(DDLParameters ddlParameters) {
        String schema = ddlParameters.getSchema();
        String tableName = ddlParameters.getTableName();
        List<DdlDTO> columns = ddlParameters.getColumns();
        String tableCnName = ddlParameters.getTableCnName();
        // 1. 字段定义
        StringBuilder ddl = new StringBuilder();
        if (StringUtils.isNotBlank(schema)) {
            tableName = schema + "." + tableName;
        }
        ddl.append("CREATE TABLE ").append(tableName).append(" (");

        // 分离普通字段和分区字段
        List<DdlDTO> normalColumns = columns.stream().filter(c -> c.getPartition() != 1).collect(Collectors.toList());
        List<DdlDTO> partitionColumns = columns.stream().filter(c -> c.getPartition() == 1).collect(Collectors.toList());

        // 普通字段
        List<String> pkList = normalColumns.stream().filter(c -> c.getPrimaryKey() == 1).map(DdlDTO::getColumnName).collect(Collectors.toList());
        for (DdlDTO col : normalColumns) {
            ddl.append(col.getColumnName()).append(" ")
                    .append(getMaxComputeTypeFromSqlType(col.getDataTypeJava(), col.getDataType(), col.getColumnSize(), col.getDecimalDigits()));

            // 主键字段必须添加 NOT NULL 约束
            if (col.getPrimaryKey() == 1) {
                ddl.append(" NOT NULL");
            }

            if (StringUtils.isNotBlank(col.getColumnCnName())) {
                ddl.append(" COMMENT '").append(col.getColumnCnName().replace("'", "''")).append("'");
            }
            ddl.append(",");
        }

        // 主键
        if (!pkList.isEmpty()) {
            ddl.append("PRIMARY KEY (").append(String.join(",", pkList)).append(") DISABLE,");
        }
        ddl.deleteCharAt(ddl.length() - 1); // 去掉最后一个逗号
        ddl.append(")");

        // 表注释
        if (StringUtils.isNotBlank(tableCnName)) {
            ddl.append(" COMMENT '").append(tableCnName.replace("'", "''")).append("'");
        }

        // 分区
        if (!partitionColumns.isEmpty()) {
            ddl.append(" PARTITIONED BY (");
            for (DdlDTO col : partitionColumns) {
                ddl.append(col.getColumnName()).append(" ")
                        .append(getMaxComputeTypeFromSqlType(col.getDataTypeJava(), col.getDataType(), col.getColumnSize(), col.getDecimalDigits()));
                if (StringUtils.isNotBlank(col.getColumnCnName())) {
                    ddl.append(" COMMENT '").append(col.getColumnCnName().replace("'", "''")).append("'");
                }
                ddl.append(",");
            }
            ddl.deleteCharAt(ddl.length() - 1);
            ddl.append(")");
        }

        // 返回
        String checkTableSql = "desc " + tableName;
        return new Tuple3<>(ddl.toString(), checkTableSql, "");
    }

    // 字段类型映射（可根据需要完善）
    private String getMaxComputeTypeFromSqlType(int dataType, String dataName, int columnSize, int decimalDigits) {
        switch (dataType) {
            case java.sql.Types.BIGINT:
                return "BIGINT";
            case java.sql.Types.INTEGER:
                return "INT";
            case java.sql.Types.SMALLINT:
                return "SMALLINT";
            case java.sql.Types.TINYINT:
                return "TINYINT";
            case java.sql.Types.FLOAT:
                return "FLOAT";
            case java.sql.Types.DOUBLE:
                return "DOUBLE";
            case java.sql.Types.DECIMAL:
            case java.sql.Types.NUMERIC:
                return "DECIMAL(" + (columnSize > 0 ? columnSize : 10) + "," + (decimalDigits > 0 ? decimalDigits : 0) + ")";
            case java.sql.Types.DATE:
                return "DATE";
            case java.sql.Types.TIMESTAMP:
                return "DATETIME";
            case java.sql.Types.BOOLEAN:
                return "BOOLEAN";
            case java.sql.Types.CHAR:
                return "CHAR(" + (columnSize > 0 ? columnSize : 10) + ")";
            case java.sql.Types.VARCHAR:
                return "STRING";
            default:
                return "STRING";
        }
    }

    @Override
    public Tuple2<String, List<String>> generateAddPkSql(String schema, String tableName, List<MetadataColumnDTO> pkFieldList, String fieldUppLower) {
        return new Tuple2<>("", null);
    }
}
