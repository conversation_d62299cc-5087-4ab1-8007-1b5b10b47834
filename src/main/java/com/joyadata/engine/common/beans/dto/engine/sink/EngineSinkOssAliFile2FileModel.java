package com.joyadata.engine.common.beans.dto.engine.sink;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class EngineSinkOssAliFile2FileModel {
    private String path;

    private String tmpPath;

    private String bucket;

    private String accessKey;

    private String accessSecret;

    private String endpoint;

    /**
     * 校验文件
     */
    private String validateFile;
    /**
     * 校验内容
     */
    private String validateContent;

    private String fileFormatType = "binary";
    /**
     * 是否文件同步
     * 文件同步:true
     * 文件夹同步:false
     */
    private String isFile;

    private String fileName;
    /**
     * 当source_table_name未指定时，当前插件处理配置文件中前一个插件输出的dataset数据集。当指定source_table_name时，当前插件处理的是对应此参数的数据集。
     */
    private String sourceTableName;
}
