package com.joyadata.engine.common.beans.utils.dialect.converter;

import com.joyadata.engine.common.beans.dto.DdlDTO;
import com.joyadata.engine.common.beans.dto.datasource.MetadataColumnDTO;
import com.joyadata.engine.common.beans.enums.PrimaryKeyModelEnum;
import com.joyadata.engine.common.beans.utils.dialect.AbstractDatabaseTypeConverter;
import com.joyadata.engine.common.beans.utils.dialect.paramters.DDLParameters;
import com.joyadata.engine.common.beans.utils.dialect.paramters.DorisDDLParameters;
import groovy.lang.Tuple2;
import groovy.lang.Tuple3;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.sql.Types;
import java.util.*;

/**
 * <AUTHOR>
 * @Date 2025/4/27 17:50
 */
@Slf4j
public class DorisTypeConverter extends AbstractDatabaseTypeConverter {
    @Override
    protected Tuple3<String, String, String> doGenerateDDL(DDLParameters ddlParameters) {
        DorisDDLParameters dorisDDLParameters = (DorisDDLParameters) ddlParameters;
        String schema = dorisDDLParameters.getSchema();
        String tableName = dorisDDLParameters.getTableName();
        List<DdlDTO> columns = dorisDDLParameters.getColumns();
        String tableCnName = dorisDDLParameters.getTableCnName();
        String fieldUppLower = dorisDDLParameters.getFieldUppLower();
        PrimaryKeyModelEnum primaryKeyModelEnum = dorisDDLParameters.getPrimaryKeyModelEnum();
        List<String> primaryKeyList = dorisDDLParameters.getPrimaryKeyList();
        // 设置主键字段排序
        setOrderNum(columns, primaryKeyList);

        // 根据主键字段，进行排序
        Collections.sort(columns, new Comparator<DdlDTO>() {
            @Override
            public int compare(DdlDTO o1, DdlDTO o2) {
                int result = o1.getOrderNum() > o2.getOrderNum() ? -1 : (o1.getOrderNum() == o2.getOrderNum() ? 0 : 1);
                return result;
            }
        });

        if (StringUtils.isNotBlank(fieldUppLower)) {
            tableName = getUppOrLowerCase(fieldUppLower, tableName);
        }
        String create = "create table `" + tableName + "` (";
        String checkTableSql = "select 1 from `" + tableName + "` where 1=2";
        if (StringUtils.isNotBlank(schema)) {
            if (StringUtils.isNotBlank(fieldUppLower)) {
                schema = getUppOrLowerCase(fieldUppLower, schema);
            }
            create = "create table `" + schema + "`.`" + tableName + "` (";
            checkTableSql = "select 1 from `" + schema + "`.`" + tableName + "` where 1=2";
        }
        StringBuilder ddl = new StringBuilder(create);
        List<Integer> hashKeyList = Arrays.asList(Types.BIT, Types.SMALLINT, Types.TINYINT, Types.INTEGER, Types.BIGINT, Types.NUMERIC, Types.DECIMAL, Types.DATE, Types.TIMESTAMP);
        Optional<String> first = columns.stream()
                .filter(ddlDTO -> hashKeyList.contains(ddlDTO.getDataTypeJava())).map(DdlDTO::getColumnName).findFirst();
        String hashKey = "";
        if (first.isPresent()) {
            hashKey = first.get();
        }
        List<String> keyList = new ArrayList<>();
        for (DdlDTO ddlDTO : columns) {
            int dataType = ddlDTO.getDataTypeJava();// Integer.valueOf(column.get("data_type"));
            int columnSize = ddlDTO.getColumnSize();// Integer.valueOf(column.get("max_length"));
            int decimalDigits = ddlDTO.getDecimalDigits(); //Integer.valueOf(column.get("decimal_digits"));
            String dataName = ddlDTO.getDataType(); //column.get("type_name");
            String columnName;
            if (StringUtils.isNotBlank(fieldUppLower)) {
                columnName = getUppOrLowerCase(fieldUppLower, ddlDTO.getColumnName());
            } else {
                columnName = ddlDTO.getColumnName();
            }
            String cnName = ddlDTO.getColumnCnName();// StringUtils.defaultString(column.get("remarks"),"");
            String dataTypeString = getDorisTypeFromSqlType(dataType, dataName, columnSize, decimalDigits);
            if (StringUtils.isNotBlank(cnName)) {
                cnName = cnName.replaceAll("'", "`");
                cnName = cnName.replaceAll("\"", "“");
                ddl.append("`").append(columnName).append("` ").append(dataTypeString).append(" COMMENT '").append(cnName).append("'").append(",");
            } else {
                ddl.append("`").append(columnName).append("` ").append(dataTypeString).append(",");
            }
            //hashKey的值优先取数值日期类的，没有的话就取VARCHAR，都没有就不给
            if ("".equals(hashKey) && "VARCHAR".equals(dataTypeString)) {
                hashKey = columnName;
            }
            int primaryKey = ddlDTO.getPrimaryKey();
            if (1 == primaryKey) {
                keyList.add("`" + ddlDTO.getColumnName() + "`");
            }
        }
        String result = ddl.toString();
        result = result.substring(0, result.length() - 1);
        Boolean hasKey = false;
        if (primaryKeyList.size() != 0) {
            hasKey = true;
            result = result + ")";
            String keyString = "`" + String.join("`,`", primaryKeyList) + "`";
            if (primaryKeyModelEnum.getName().equalsIgnoreCase(PrimaryKeyModelEnum.UNIQUE.getName())) {
                result = result + "UNIQUE KEY(" + keyString + ")";
            } else if (primaryKeyModelEnum.getName().equalsIgnoreCase(PrimaryKeyModelEnum.DUPLICATE.getName())) {
                result = result + "DUPLICATE KEY(" + keyString + ")";
            }
            hashKey = keyString;//如果有主键则指定的分布列（distribution column）必须是唯一键的一部分
        }
        //表中文名
        if (StringUtils.isNotBlank(tableCnName)) {
            tableCnName = tableCnName.replaceAll("'", "`");
            tableCnName = tableCnName.replaceAll("\"", "“");
            if (!hasKey) {
                result = result + ") ";
            }
            if (primaryKeyList.size() != 0) {
                result = result + "COMMENT ='" + tableCnName + "' DISTRIBUTED BY HASH(" + hashKey + ") BUCKETS AUTO  PROPERTIES (\"replication_num\" = \"1\")";
            } else {

                result = result + "COMMENT ='" + tableCnName + "' DISTRIBUTED BY HASH(`" + hashKey + "`) BUCKETS AUTO  PROPERTIES (\"replication_num\" = \"1\")";
            }
        } else {
            if (!hasKey) {
                result = result + ") ";
            }
            if (primaryKeyList.size() != 0) {

                result = result + " DISTRIBUTED BY HASH(" + hashKey + ") BUCKETS AUTO  PROPERTIES (\"replication_num\" = \"1\")";
            } else {

                result = result + " DISTRIBUTED BY HASH(" + "`" + hashKey + "`" + ") BUCKETS AUTO  PROPERTIES (\"replication_num\" = \"1\")";
            }
        }
        log.info("getDorisDDLFromMetadata 生成doris建表语句是 {}", result);
        log.info("getDorisDDLFromMetadata 生成doris检测表是否存在的语句是 {}", checkTableSql);
        Tuple3<String, String, String> tuple = new Tuple3<>(result, "", checkTableSql);//doris 备注在sql中
        return tuple;
    }

    private static String getDorisTypeFromSqlType(int dataType, String dataName, int columnSize, int decimalDigits) {
        switch (dataType) {
            case Types.BOOLEAN:
                return "BOOLEAN";
            case Types.BIT:
            case Types.SMALLINT:
                return "SMALLINT";
            case Types.TINYINT:
                return "TINYINT";
            case Types.INTEGER:
            case Types.BIGINT:
                return "BIGINT";
            case Types.FLOAT:
                return "FLOAT";
            case Types.DOUBLE:
                return "DOUBLE";
            case Types.NUMERIC:
                if (decimalDigits == 0) {
                    if (columnSize < 19) {
                        return "BIGINT";
                    } else if (columnSize > 65) {
                        return "DECIMAL( 65 )";
                    } else {
                        return "DECIMAL(" + columnSize + ")";
                    }
                } else if (decimalDigits == -127) {
                    if (columnSize == 0 || columnSize > 65) {
                        return "DECIMAL(" + 65 + ", " + 0 + ")";
                    } else {
                        return "DECIMAL(" + columnSize + ", " + 0 + ")";
                    }
                } else {
                    if ("float4".equalsIgnoreCase(dataName) || "float8".equalsIgnoreCase(dataName)) {//pg字段处理
                        return "DOUBLE";
                    }
                    return "DECIMAL(" + columnSize + ", " + decimalDigits + ")";
                }
            case Types.DECIMAL:
                return "DECIMAL(" + columnSize + ", " + decimalDigits + ")";
            case Types.DATE:
                return "DATE";
            case Types.TIME:
                return "TIME";
            case Types.TIMESTAMP:
                return "DATETIME";
            case Types.BINARY:
            case Types.VARBINARY:
            case Types.LONGVARBINARY:
                return "VARCHAR";
            case Types.ARRAY:
                return "ARRAY";
            case Types.CHAR:
            case Types.VARCHAR:
            case Types.NCHAR:
            case Types.NVARCHAR:
            case Types.LONGNVARCHAR:
            case Types.LONGVARCHAR:
            case Types.CLOB:
            case Types.NCLOB:
            case Types.BLOB:
                if (columnSize > 0 && columnSize < 65533) {
                    return "VARCHAR(" + columnSize + ")";
                } else {
                    return "TEXT";
                }
            default:
                if (StringUtils.isNotBlank(dataName)) {
                    if ("NVARCHAR2".equalsIgnoreCase(dataName)) {
                        return "VARCHAR";
                    } else if ("VARCHAR2".equalsIgnoreCase(dataName)) {
                        return "VARCHAR";
                    } else if (dataName.startsWith("TIMESTAMP")) {
                        return "DATETIME";
                    } else if ("INTERVAL YEAR(2) TO MONTH".equalsIgnoreCase(dataName)) {
                        return "VARCHAR";
                    } else if (dataName.startsWith("TIMESTAMP")) {
                        return "DATETIME";
                    } else if (dataName.equalsIgnoreCase("serial")) {//pg数据库转int
                        return "INT";
                    }
                }
                return "VARCHAR(255)";
        }
    }

    private static void setOrderNum(List<DdlDTO> currentColumns, List<String> primaryKeyList) {
        Map<String, Integer> columnIndexMap = new HashMap<>();
        int size = primaryKeyList.size();
        for (int i = 0; i < primaryKeyList.size(); i++) {
            String column = primaryKeyList.get(i);
            columnIndexMap.put(column, size - i);
        }

        for (DdlDTO ddl : currentColumns) {
            if (primaryKeyList.contains(ddl.getColumnName())) {
                ddl.setPrimaryKey(1); // 以指定的主键为准
                Integer index = columnIndexMap.get(ddl.getColumnName());
                ddl.setOrderNum(index);
            } else {
                ddl.setPrimaryKey(0);
            }
        }
    }

    @Override
    public Tuple2<String, List<String>> generateAddPkSql(String schema, String tableName, List<MetadataColumnDTO> pkFieldList, String fieldUppLower) {
        return new Tuple2<>("", null);
    }
}
