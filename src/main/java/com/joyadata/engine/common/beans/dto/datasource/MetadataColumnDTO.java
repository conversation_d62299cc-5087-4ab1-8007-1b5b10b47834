package com.joyadata.engine.common.beans.dto.datasource;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version V1.0
 * @ClassName: MetadataColumnDTO
 * @date 2024/2/22
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class MetadataColumnDTO {

    /**
     * 主键
     */
    private String id;

    /**
     * 表id
     */
    private String metadataTableUuid;

    /**
     * 字段名称
     */
    private String columnName;

    /**
     * 字段中文名称
     */
    private String columnCnName;

    /**
     * 字段别名
     */
    private String columnAlias;

    /**
     * 字段排序
     */
    private Long columnSort;

    /**
     * 字段长度
     */
    private String columnLength;

    /**
     * 数据精度
     */
    private String columnScale;

    /**
     * 字段类型
     */
    private String columnType;

    /**
     * 字段注释
     */
    private String columnComment;

    /**
     * 是否是主键字段 0不是1是
     */
    private String columnPrimaryKey;

    /**
     * 是否是外键 0不是1是
     */
    private String columnForeignKey;

    /**
     * 是否增量字段 0不是1是
     */
    private String columnIncremental;

    /**
     * 数据类型
     */
    private String columnDataType;

    /**
     * SQL类型,java.sql.Type
     */
    private String dataType;

    /**
     * 分区字段，0不是1是
     */
    private String partitionColumn;

}
