package com.joyadata.engine.common.beans.dto.engine.source;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.LinkedHashMap;

/**
 * <AUTHOR>
 * @date 2024/12/18
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class EngineSourceArgoHdfsFileModel implements Serializable {
    private String resultTableName;
    private String defaultFS;

    private String path;

    /**
     * 文件类型
     */
    private String fileFormatType = "text";
    /**
     * 数据分隔符
     * Only used when file_format_type is text
     */
    private String fieldDelimiter;
    /**
     * 每行的数据分隔符
     * Only used when file_format_type is text
     */
    private String rowDelimiter;


    private String argoUrl;
    private String argoUser;
    private String argoPassword;
    private String argoSchema;
    private String argoTable;
    private String argoTmpTableName;
    private String argoTmpFilePath;

    private LinkedHashMap<String, String> argoSchemas;

    private String krb5Path;
    private String kerberosPrincipal;
    private String kerberosKeytabPath;
    private String hdfsSitePath;
    /**
     * 空值处理
     */
    private String emptyDataStrategy;
    /**
     * 外表schema
     */
    private String argoTmpSchema;
}
