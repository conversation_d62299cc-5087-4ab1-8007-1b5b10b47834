package com.joyadata.engine.common.beans.dto.engine.source;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/2/21
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class EngineSourceOracleCDCModel implements Serializable {
    /**
     * 如果未指定`result_table_name`，则该插件处理的数据不会注册为可被其他插件直接访问的数据集`(dataStream/dataset)`，
     * 也称为临时表`(table)`；如果指定了`result_table_name`，则该插件处理的数据会注册为可被其他插件直接访问的数据集`(dataStream/dataset)`，
     * 也称为临时表`(table)`。这里注册的数据集`(dataStream/dataset)`，其他插件通过指定`source_table_name`可直接进行访问。
     * <a href="https://seatunnel.apache.org/docs/2.3.3/connector-v2/source/common-options" /a>
     */
    private String resultTableName;
    /**
     * 用户名
     */
    private String username;
    /**
     * 密码
     */
    private String password;
    /**
     * 数据库名称。多个用逗号分割。
     */
    private String databaseNames;
    /**
     * schema名称
     */
    private String schemaName;
    /**
     * 表明，多个用逗号分割
     */
    private String tableNames;
    /**
     * jdbc url
     * 示例："*********************************************"
     */
    private String baseUrl;
    /**
     * 关闭超时时间
     */
    private String sourceReaderCloseTimeout;
    /**
     * CDC源的可选启动模式，有效的枚举是“initial”，“earliest”，“latest”，“timestamp”或“specific”
     */
    private String startupMode;
    /**
     * Debezium嵌入式引擎 配置
     * 参考：     debezium {
     * log.mining.strategy = "online_catalog"
     * log.mining.continuous.mine = "true"
     * database.oracle.jdbc.timezoneAsRegion = "false"
     * }
     */
    private Map<String, String> debezium;


}
