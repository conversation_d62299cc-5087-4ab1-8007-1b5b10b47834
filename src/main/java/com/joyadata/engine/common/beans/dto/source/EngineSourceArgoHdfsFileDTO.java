package com.joyadata.engine.common.beans.dto.source;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.LinkedHashMap;

/**
 * <AUTHOR>
 * @date 2024/12/18
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class EngineSourceArgoHdfsFileDTO extends EngineBaseSourceDTO implements Serializable {
    private String defaultFS;

    /**
     * 外表文件路径(hdfs路径)
     */
    private String path;

    /**
     * 文件类型
     */
    private String fileFormatType = "text";
    /**
     * 外表列分隔符
     * Only used when file_format_type is text
     */
    private String fieldDelimiter = ",";
    /**
     * 外部表行分隔符
     * Only used when file_format_type is text
     */
    private String rowDelimiter = "\\n";


    private String argoUrl;
    private String argoUser;
    private String argoPassword;
    private String argoSchema;

    /**
     * 表名称
     */
    private String argoTable;

    /**
     * 外表名称
     */
    private String argoTmpTableName;
    private String argoTmpFilePath;

    private LinkedHashMap<String, String> argoSchemas;

    private String krb5Path;
    private String kerberosPrincipal;
    private String kerberosKeytabPath;
    private String hdfsSitePath;
    /**
     * 空值处理
     */
    private String emptyDataStrategy;
    /**
     * 外表schema
     */
    private String argoTmpSchema;

}
