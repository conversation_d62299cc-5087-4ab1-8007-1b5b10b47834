package com.joyadata.engine.common.beans.dto.engine.sink;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/2/21
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class EngineSinkKafkaModel implements Serializable {
    /**
     * kafka主题
     */
    private String topic;
    /**
     * 服务器地址。如"192.168.90.104:9092"
     */
    private String bootstrapServers;
    /**
     * 请求超时时间（毫秒）
     */
    private String kafkaRequestTimeoutMs;
    /**
     * 数据格式
     */
    private String format;
    /**
     * 语义。如 AT_LEAST_ONCE
     */
    private String semantics;
    /**
     * 字段分隔符
     */
    private String fieldDelimiter;
    /**
     * 分区
     */
    private String partition;
    /**
     * kafka配置，如：
     * {
     * acks = "all"
     * request.timeout.ms = 60000
     * buffer.memory = 33554432
     * }
     */
    private Map<String, String> kafkaConfig;

    /**
     * 当source_table_name未指定时，当前插件处理配置文件中前一个插件输出的dataset数据集。当指定source_table_name时，当前插件处理的是对应此参数的数据集。
     */
    private String sourceTableName;

}
