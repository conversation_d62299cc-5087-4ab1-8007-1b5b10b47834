package com.joyadata.engine.common.beans.dto;

import com.joyadata.engine.common.beans.dto.datasource.MetadataColumnDTO;
import com.joyadata.engine.common.beans.enums.DatabaseTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class CreatePkDTO {
    //数据库类型
    private DatabaseTypeEnum type;
    private String schema;
    private String tableName;
    private List<MetadataColumnDTO> fieldList;
    /**
     * 转大小写
     */
    private String fieldUppLower;
}
