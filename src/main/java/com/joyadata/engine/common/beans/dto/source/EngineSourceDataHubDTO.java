package com.joyadata.engine.common.beans.dto.source;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/7/3 11:18
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class EngineSourceDataHubDTO extends EngineBaseSourceDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    private String endPoint;

    private String accessId;

    private String accessKey;

    private String project;

    private String topic;

    private String customTimestamp;//游标获取方式为SYSTEM_TIME，指定开始时间戳

    private Long partitionMillis;//分区发现间隔毫秒

    private String cursorMode;//消费模式

    private String endTimestampMillis;//消费结束时间

    /**
     *   schema = {
     *       fields {
     *         name = "string"
     *       }
     *     }
     */
    private LinkedHashMap<String, String> schema;
    private String subId;//订阅者

}