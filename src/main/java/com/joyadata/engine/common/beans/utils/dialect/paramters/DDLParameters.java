package com.joyadata.engine.common.beans.utils.dialect.paramters;

import com.joyadata.engine.common.beans.dto.DdlDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/4/29 15:09
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class DDLParameters {
    protected String schema;
    protected String tableName;
    protected List<DdlDTO> columns;
    protected String tableCnName;
    protected String fieldUppLower;
}
