package com.joyadata.engine.common.beans.utils;

import com.joyadata.engine.common.beans.dto.CreatePkDTO;
import com.joyadata.engine.common.beans.dto.CreateTableDTO;
import com.joyadata.engine.common.beans.dto.datasource.MetadataColumnDTO;
import com.joyadata.engine.common.beans.utils.dialect.SqlFactoryDialect;
import groovy.lang.Tuple2;
import groovy.lang.Tuple3;

import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

public class EngineUtilsCommon {

    /**
     * @param createTableDTO 建表对象
     * @return
     */
    public static Tuple3<String, String, String> getDdlByMetadata(CreateTableDTO createTableDTO) throws Exception {
        return SqlFactoryDialect.generateDDL(createTableDTO);
    }

    /**
     * type
     * schema
     * tableName
     * columnDTOList
     * fieldUppLower
     *
     * @return
     */
    public static Tuple2<String, List<String>> getADDPkSql(CreatePkDTO createPkDTO) throws Exception {
        List<MetadataColumnDTO> fieldList = createPkDTO.getFieldList();
        if (null == fieldList || fieldList.isEmpty()) {
            throw new Exception("column list is empty");
        }
        List<MetadataColumnDTO> columnDTOList = fieldList.stream().sorted(Comparator.comparingLong(MetadataColumnDTO::getColumnSort)).collect(Collectors.toList());
        List<MetadataColumnDTO> pkFieldList = columnDTOList.stream().filter(m -> "1".equals(m.getColumnPrimaryKey())).collect(Collectors.toList());
        if (pkFieldList.isEmpty()) {
            throw new Exception("primary key is empty");
        }
        return SqlFactoryDialect.generateAddPkSql(createPkDTO.getType().getName(), createPkDTO.getSchema(), createPkDTO.getTableName(), pkFieldList, createPkDTO.getFieldUppLower());
    }
}
