package com.joyadata.engine.common.beans.dto.engine.sink;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class EngineMaxComputeBaseSinkModel {
    /**
     * 必填项 accessId:入口id，eg:LTAI5tAr5vXokEu4EA7t8vP9
     */
    private String accessId;
    /**
     * 必填项 accessKey:密钥，eg:******************************
     */
    private String accessKey;
    /**
     * 必填项 endpoint：请求url，eg:<a href="https://xxxe.aliyun.com/api">...</a>
     */
    private String endpoint;
    /**
     * endpoint：请求url，eg:<a href="https://xxxe.aliyun.com/api">...</a>
     */
    private String tunnelEndpoint;
    /**
     * 必填项 project:项目名称，在阿里云上创建的项目名称，eg:test_pro
     */
    private String project;
    /**
     * 必填项 table_name:表名，eg:test_table
     */
    private String tableName;
    /**
     * 非必填 partition_spec:分区规格，eg:ds='20220101'
     */
    private String partitionSpec;
    /**
     * 非必填 overwrite:是否覆盖表，默认是false
     */
    private String isCoverTable = "false";
    /**
     * 非必填 source_table_name:上游表名，用于连接source或者transform，eg:fake
     */
    private String sourceTableName;
}
