package com.joyadata.engine.common.beans.dto.engine.sink;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2024/9/24 18:01.
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class EngineSinkElasticsearchModel extends EngineBaseSinkModel implements Serializable {
    /**
     * 数据源id
     */
    private String datasourceInfoId;

    /**
     * Elasticsearch cluster http address, the format is host:port , allowing multiple hosts to be specified. Such as ["host1:9200", "host2:9200"].
     */
    private String hosts;

    /**
     * Elasticsearch index name
     */
    private String index;

    /**
     * Elasticsearch index type, it is recommended not to specify in elasticsearch 6 and above
     */
    private String indexType;

    /**
     * username
     */
    private String username;

    /**
     * userpasswordname
     */
    private String password;

    /**
     * one bulk request max try size
     */
    private String maxRetryCount;

    /**
     * batch bulk doc max size
     */
    private String maxBatchSize;

    /**
     * Enable certificates validation for HTTPS endpoints
     */
    private String tlsVerifyCertificate;

    /**
     * Enable hostname validation for HTTPS endpoints
     */
    private String tlsVerifyHostname;

    /**
     * Before the synchronous task is turned on, different treatment schemes are selected for the existing surface structure of the target side. Option introduction：
     * RECREATE_SCHEMA ：Will create when the table does not exist, delete and rebuild when the table is saved
     * CREATE_SCHEMA_WHEN_NOT_EXIST ：Will Created when the table does not exist, skipped when the table is saved
     * ERROR_WHEN_SCHEMA_NOT_EXIST ：Error will be reported when the table does not exist
     */
    private String schemaSaveMode;

    /**
     * Before the synchronous task is turned on, different processing schemes are selected for data existing data on the target side. Option introduction：
     * DROP_DATA： Preserve database structure and delete data
     * APPEND_DATA：Preserve database structure, preserve data
     * ERROR_WHEN_DATA_EXISTS：When there is data, an error is reported
     */
    private String dataSaveMode;
}
