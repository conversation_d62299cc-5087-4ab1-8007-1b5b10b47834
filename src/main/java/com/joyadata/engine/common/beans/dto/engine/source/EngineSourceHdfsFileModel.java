package com.joyadata.engine.common.beans.dto.engine.source;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @version V1.0
 * @ClassName: EngineSourceHdfsFileDTO
 * @date 2024/5/28
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class EngineSourceHdfsFileModel implements Serializable {
    /**
     * 如果未指定`result_table_name`，则该插件处理的数据不会注册为可被其他插件直接访问的数据集`(dataStream/dataset)`，
     * 也称为临时表`(table)`；如果指定了`result_table_name`，则该插件处理的数据会注册为可被其他插件直接访问的数据集`(dataStream/dataset)`，
     * 也称为临时表`(table)`。这里注册的数据集`(dataStream/dataset)`，其他插件通过指定`source_table_name`可直接进行访问。
     * https://seatunnel.apache.org/docs/2.3.3/connector-v2/source/common-options
     */
    private String resultTableName;
    /**
     * 源文件路径。
     */
    private String path;
    /**
     * 我们支持以下文件类型：text json csv orc parquet excel。请注意，最终文件名将以file_format的后缀结尾，文本文件的后缀为txt。
     */
    private String fileFormatType;
    /**
     * 以hdfs://开头的hadoop集群地址，例如：hdfs://hadoopcluster
     */
    private String fsDefaultFS;
    /**
     * 读取数据源的列列表，用户可以使用它来实现字段投影。文件类型支持如下所示的列投影：[text，json，csv，orc，parquet，excel]。提示：如果用户想在读取文本jsoncsv文件时使用此功能，则必须配置schema选项。
     */
    private String readColumns;
    /**
     * hdfs-site.xml的路径，用于加载名称节点的ha配置
     */
    private String hdfsSitePath;
    /**
     * 字段分隔符，用于告诉连接器在读取文本文件时如何分割字段。default\001，与配置单元的默认分隔符相同
     */
    private String fieldDelimiter;
    /**
     * 控制是否从文件路径解析分区键和值。例如，如果从路径读取文件hdfs://hadoop-cluster/tmp/seatunnel/parquet/name=tyrantlucifer/age=26
     * .文件中的每个记录数据都将添加这两个字段：[名称：tyrantlucifer，年龄：26]。提示：不要在架构选项中定义分区字段。
     */
    private String parsePartitionFromPath;
    /**
     * 日期类型格式，用于告诉连接器如何将字符串转换为日期，支持以下格式：yyyy-MM-dd-yyyy。MM.dd yyyy/MM/dd默认yyyy-MM-dd.日期类型格式，用于告诉连接器如何将字符串转换为日期，支持以下格式：yyyy-MM-dd-yyyy。MM.dd yyyy/MM/dd默认yyyy-MM-dd
     */
    private String dateFormat;
    /**
     * 日期时间类型格式，用于告诉连接器如何将字符串转换为日期时间，支持以下格式：yyyy-MM-dd HH:MM:ss-yyyy。MM.dd HH:MM:ss yyyy/MM/dd HH:ms:ss yyyyMMddHHmmss。默认yyyy-MM-dd HH:MM:ss
     */
    private String datetimeFormat;
    /**
     * 时间类型格式，用于告诉连接器如何将字符串转换为时间，支持以下格式：HH:mm:ss HH:mm:ss。SSS.default HH:mm:ss
     */
    private String timeFormat;
    /**
     * 用于连接到hadoop登录名的登录用户。它旨在用于RPC中的远程用户，它没有任何凭据。
     */
    private String remoteUser;
    /**
     * kerberos的krb5路径
     */
    private String krb5Path;
    /**
     * kerberos的校长
     */
    private String kerberosPrincipal;
    /**
     * kerberos的keytab路径
     */
    private String kerberosKeytabPath;
    /**
     * 跳过前几行，但仅限于txt和csv。例如，设置如下：skip_header_row_number=2。然后Seatunnel将跳过源文件的前两行
     */
    private Long skipHeaderRowNumber;
    /**
     * 上游数据的模式字段
     */
    private LinkedHashMap<String, String> schema;
    /**
     * 读取工作簿的工作表，仅当file_format为excel时使用
     */
    private String sheetName;
    /**
     * 文件的压缩编解码器
     */
    private String compressCodec;
}
