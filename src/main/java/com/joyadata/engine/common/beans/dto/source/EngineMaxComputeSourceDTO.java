package com.joyadata.engine.common.beans.dto.source;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.LinkedHashMap;

@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class EngineMaxComputeSourceDTO implements Serializable {

    private String datasourceInfoId;
    /**
     * 必填项 table_name:表名，eg:test_table
     */
    private String tableName;
    /**
     * projectNmae
     */
    private String projectName;
    /**
     * 是否分区:1：分区表，0：无分区
     */
    private Integer isPartitionType;
    /**
     * 分区值 非必填 partition_spec:分区规格，eg:ds='20220101'
     */
    private String partitionValue;
    /**
     * 非必填 split_row:分片行数，默认10000行
     */
    private Integer splitRow = 10000;
    /**
     * 非必填 parallelism:并行度
     */
    private Integer parallelism;
    /**
     * 非必填 schema:上游数据的架构信息
     */
    private LinkedHashMap<String, String> schema;

}
