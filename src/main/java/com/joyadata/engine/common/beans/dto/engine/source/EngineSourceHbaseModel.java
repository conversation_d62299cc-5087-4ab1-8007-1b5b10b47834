package com.joyadata.engine.common.beans.dto.engine.source;

import com.joyadata.engine.common.beans.dto.source.EngineBaseSourceDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.LinkedHashMap;
import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2024/9/26 11:01.
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@EqualsAndHashCode(callSuper = true)
@Data
public class EngineSourceHbaseModel extends EngineBaseSourceModel {
    /**
     * zookeeper_quorum
     */
    private String zookeeperQuorum;
    /**
     * 表名
     */
    private String tableName;

    /**
     * query_columns
     */
    private String queryColumns;

    /**
     * schema
     */
    private List<String> columns;

    /**
     * kerberos认证的文件的所属路径
     */
    private String filePath;

    /**
     * kerberos认证的用户名
     */
    private String user;

    /**
     * serverPrincipal
     */
    private String serverPrincipal;
}
