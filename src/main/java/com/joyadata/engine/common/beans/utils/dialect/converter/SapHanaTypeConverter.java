package com.joyadata.engine.common.beans.utils.dialect.converter;

import com.alibaba.fastjson.JSON;
import com.joyadata.engine.common.beans.dto.DdlDTO;
import com.joyadata.engine.common.beans.dto.datasource.MetadataColumnDTO;
import com.joyadata.engine.common.beans.utils.dialect.AbstractDatabaseTypeConverter;
import com.joyadata.engine.common.beans.utils.dialect.paramters.DDLParameters;
import groovy.lang.Tuple2;
import groovy.lang.Tuple3;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.sql.Types;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2025/4/27 17:50
 */
@Slf4j
public class SapHanaTypeConverter extends AbstractDatabaseTypeConverter {
    @Override
    protected Tuple3<String, String, String> doGenerateDDL(DDLParameters ddlParameters) {
        String schema = ddlParameters.getSchema();
        String tableName = ddlParameters.getTableName();
        List<DdlDTO> columns = ddlParameters.getColumns();
        String tableCnName = ddlParameters.getTableCnName();
        List<String> list = new ArrayList<>();
        String checkTableSql = String.format("select 1 from %s where 1=2", tablePath(schema, tableName));
        String create = String.format("create table %s (", tablePath(schema, tableName));
        StringBuilder ddl = new StringBuilder(create);
        columns.forEach(ddlDTO -> {
            int dataType = ddlDTO.getDataTypeJava();
            int columnSize = ddlDTO.getColumnSize();
            int decimalDigits = ddlDTO.getDecimalDigits();
            String dataName = ddlDTO.getDataType();
            String columnName;
            columnName = ddlDTO.getColumnName().toUpperCase();
            String cnName = ddlDTO.getColumnCnName();
            String dataTypeString = getSapHanaTypeFromSqlType(dataType, dataName, columnSize, decimalDigits);
            ddl.append("\"").append(columnName).append("\" ").append(dataTypeString).append(",");
            if (StringUtils.isNotBlank(cnName)) {
                cnName = cnName.replaceAll("'", "`");
                cnName = cnName.replaceAll("\"", "“");
                if (StringUtils.isNotBlank(schema)) {
                    list.add("COMMENT ON COLUMN " + schema + "." + tableName + "." + columnName + " IS '" + cnName + "'");
                } else {
                    list.add("COMMENT ON COLUMN " + tableName + "." + columnName + " IS '" + cnName + "'");
                }
            }
        });
        if (StringUtils.isNotBlank(tableCnName)) {
            tableCnName = tableCnName.replaceAll("'", "`");
            tableCnName = tableCnName.replaceAll("\"", "“");
            if (StringUtils.isNotBlank(schema)) {
                list.add("COMMENT ON TABLE  " + schema + "." + tableName + " IS '" + tableCnName + "'");
            } else {
                list.add("COMMENT ON TABLE  " + tableName + " IS '" + tableCnName + "'");
            }
        }
        String result = ddl.toString();
        result = result.substring(0, result.length() - 1);
        result = result + ")";
        log.info("SapHanaFromMetadata生成SapHana建表语句是 {}", result);
        log.info("SapHanaFromMetadata生成SapHana检测表是否存在的语句是 {}", checkTableSql);
        Tuple3<String, String, String> tuple3 = new Tuple3<>(result, JSON.toJSONString(list), checkTableSql);
        return tuple3;
    }

    public static String getSapHanaTypeFromSqlType(int dataType, String dataName, int columnSize, int decimalDigits) {
        switch (dataType) {
            case Types.BOOLEAN:
                return "BOOLEAN";
            case Types.BIT:
            case Types.TINYINT:
                return "TINYINT";
            case Types.SMALLINT:
                return "SMALLINT";
            case Types.INTEGER:
                return "INT";
            case Types.BIGINT:
                return "BIGINT";
            case Types.FLOAT:
                return "FLOAT";
            case Types.REAL:
                return "REAL";
            case Types.DOUBLE:
                return "DOUBLE";
            case Types.NUMERIC:
            case Types.DECIMAL:
                if (decimalDigits == 0) {
                    if (columnSize == 1) {
                        return "TINYINT";
                    } else if (columnSize < 3) {
                        return "SMALLINT";
                    } else if (columnSize < 5) {
                        return "MEDIUMINT";
                    } else if (columnSize < 10) {
                        return "INT";
                    } else if (columnSize < 19) {
                        return "BIGINT";
                    } else if (columnSize > 65) {
                        return "DECIMAL( 65 )";
                    } else {
                        return "DECIMAL(" + columnSize + ")";
                    }
                } else if (decimalDigits == -127) {
                    if (columnSize == 0 || columnSize > 65) {
                        return "DECIMAL(" + 65 + ", " + 0 + ")";
                    } else {
                        return "DECIMAL(" + columnSize + ", " + 0 + ")";
                    }
                } else {
                    if ("float4".equalsIgnoreCase(dataName) || "float8".equalsIgnoreCase(dataName)) {//pg字段处理
                        return "DOUBLE";
                    }
                    return "DECIMAL(" + columnSize + ", " + decimalDigits + ")";
                }
            case Types.DATE:
                return "DATE";
            case Types.TIME:
                return "TIME";
            case Types.TIMESTAMP:
                return "TIMESTAMP";
            case Types.BINARY:
            case Types.VARBINARY:
            case Types.LONGVARBINARY:
                return "BLOB";
            case Types.CHAR:
            case Types.VARCHAR:
            case Types.NCHAR:
            case Types.NVARCHAR:
            case Types.LONGNVARCHAR:
            case Types.LONGVARCHAR:
                if (columnSize > 0 && columnSize <= 255) {
                    return "VARCHAR(" + columnSize + ")";
                } else {
                    return "CLOB";
                }
            case Types.CLOB:
                return "CLOB";
            case Types.NCLOB:
                return "NCLOB";
            case Types.BLOB:
                return "BLOB";
            default:
                if (StringUtils.isNotBlank(dataName)) {
                    if ("NVARCHAR2".equalsIgnoreCase(dataName)) {
                        return "VARCHAR(" + columnSize + ")";
                    } else if ("VARCHAR2".equalsIgnoreCase(dataName)) {
                        return "VARCHAR(" + columnSize + ")";
                    } else if (dataName.startsWith("TIMESTAMP")) {
                        return "TIMESTAMP";
                    } else if ("INTERVAL YEAR(2) TO MONTH".equalsIgnoreCase(dataName)) {
                        return "VARCHAR(10)";
                    } else if (dataName.equalsIgnoreCase("serial")) {//pg数据库转int
                        return "INT";
                    }
                }
                return "CLOB";
        }
    }

    @Override
    public Tuple2<String, List<String>> generateAddPkSql(String schema, String tableName, List<MetadataColumnDTO> fieldList, String fieldUppLower) {
        List<String> pkFieldList = fieldList.stream().map(MetadataColumnDTO::getColumnName).collect(Collectors.toList());
        if (StringUtils.isNotBlank(fieldUppLower)) {
            tableName = getUppOrLowerCase(fieldUppLower, tableName);
            pkFieldList = convertListCase(pkFieldList, fieldUppLower);
        }
        String pkSql = String.format("ALTER TABLE %s ADD PRIMARY KEY (%s) ", tablePath(schema, tableName),pkFieldList
                .stream()
                .map(this::quoteIdentifier)
                .collect(Collectors.joining(",")));
        Tuple2<String, List<String>> tuple2 = new Tuple2<>(pkSql, null);
        return tuple2;
    }

    @Override
    public String qute() {
        return "\"";
    }
}
