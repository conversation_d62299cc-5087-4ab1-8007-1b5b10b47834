package com.joyadata.engine.common.beans.dto.engine.sink;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/12/18
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class EngineSinkAdbGpdistModel implements Serializable {

    /**
     * 数据源id
     */
    private String datasourceInfoId;

    private String Path;

    private String fileFormatType;

    /**
     * 仅当file_format_type为文本时使用
     */
    private String fieldDelimiter;

    private String adbUrl;

    private String adbDriver;

    private String adbUser;

    private String adbPassword;

    private String adbDatabase;
    
    private String schemaName;

    private String adbTable;

    private String adbGpfdistDddress;

    private String adbTmpFilePath;


    /**
     * 当source_table_name未指定时，当前插件处理配置文件中前一个插件输出的dataset数据集。当指定source_table_name时，当前插件处理的是对应此参数的数据集。
     */
    private String sourceTableName;
    /**
     * gpfdist路径
     */
    private String adbGpfdistPath;

    /**
     * 外表名
     */
    private String adbExternalTableName;
    /**
     * 外表schema
     */
    private String adbExternalTableSchema;
}
