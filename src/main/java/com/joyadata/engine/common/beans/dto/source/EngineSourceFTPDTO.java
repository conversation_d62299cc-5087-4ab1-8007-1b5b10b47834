package com.joyadata.engine.common.beans.dto.source;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.LinkedHashMap;
import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2024/5/28 10:21.
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class EngineSourceFTPDTO implements Serializable {
    /**
     * 数据源id
     */
    private String datasourceInfoId;
    /**
     * 服务器ip
     */
    private String host;
    /**
     * 服务器端口
     */
    private String port;
    /**
     * 服务器用户名
     */
    private String user;
    /**
     * 服务器密码
     */
    private String password;
    /**
     * 文件路径
     */
    private String path;

    /**
     * 连接模式
     * The target ftp connection mode , default is active mode, supported as the following modes:
     * active_local passive_local
     */
    private String connectionMode;
    /**
     * 文件类型
     */
    private String fileFormatType;
    /**
     * Filter pattern, which used for filtering files.
     * 过滤模式，用于过滤文件。
     */
    private String fileFilterPattern;
    /**
     * 数据分隔符
     */
    private String fieldDelimiter;
    /**
     * Control whether parse the partition keys and values from file path
     * For example if you read a file from path oss://hadoop-cluster/tmp/seatunnel/parquet/name=tyrantlucifer/age=26
     * Every record data from file will be added these two fields:
     * name age
     * tyrantlucifer 26
     * Tips: Do not define partition fields in schema option
     */
    private String parsePartitionFromPath;
    /**
     * Date type format, used to tell connector how to convert string to date,supported as the following formats:yyyy-MM-dd yyyy.MM.dd yyyy/MM/dddefault yyyy-MM-dd
     * 日期类型格式，用于告诉连接器如何将字符串转换为日期，支持如下格式:yyyy-MM-dd yyyy.MM.dd yyyy/MM/dddefault yyyy-MM-dd
     */
    private String dateFormat = "yyyy-MM-dd";

    /**
     * Datetime type format, used to tell connector how to convert string to datetime, supported as the following formats:yyyy-MM-dd HH:mm:ss yyyy.MM.dd HH:mm:ss yyyy/MM/dd HH:mm:ssyyyyMMddHHmmssdefault yyyy-MM-dd HH:mm:ss
     * 日期时间类型格式，用于告诉连接器如何将字符串转换为日期时间，支持如下格式:yyyy- mm -dd HH:mm:ssyyyy . mm .dd HH:mm:ssyyyy / mm /dd HH:mm:ssyyyyMMddHHmmssdefault yyyy- mm -dd HH:mm:ss
     */
    private String datetimeFormat = "yyyy-MM-dd HH:mm:ss";
    /**
     * Time type format, used to tell connector how to convert string to time, supported as the following formats:
     * HH:mm:ss HH:mm:ss.SSS
     * default HH:mm:ss
     */
    private String timeFormat = "HH:mm:ss";
    /**
     * Skip the first few lines, but only for the txt and csv.
     * For example, set like following:
     * skip_header_row_number = 2
     * then SeaTunnel will skip the first 2 lines from source files
     */
    private String skipHeaderRowNumber;
    /**
     * The read column list of the data source, user can use it to implement field projection.
     */
    private List<String> readColumns;
    /**
     * Reader the sheet of the workbook,Only used when file_format is excel.
     */
    private String sheetName;
    /**
     * The schema of upstream data.
     */
    private LinkedHashMap<String, String> schema;
    /**
     * The compress codec of files and the details that supported as the following shown:
     * - txt: lzo None
     * - json: lzo None
     * - csv: lzo None
     * - orc: lzo snappy lz4 zlib None
     * - parquet: lzo snappy lz4 gzip brotli zstd None
     * Tips: excel type does Not support any compression format
     */
    private String compressCodec;

}
