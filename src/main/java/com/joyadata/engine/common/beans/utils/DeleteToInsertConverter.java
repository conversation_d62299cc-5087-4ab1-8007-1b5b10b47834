package com.joyadata.engine.common.beans.utils;

import java.io.BufferedReader;
import java.io.BufferedWriter;
import java.io.FileReader;
import java.io.FileWriter;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class DeleteToInsertConverter {

    public static void main(String[] args) {
        String inputFile = "d:\\restore_to_1930_executable.sql";
        String outputFile = "d:\\11.sql";

        try {
            convertDeleteToInsert(inputFile, outputFile);
            System.out.println("Conversion completed successfully!");
        } catch (IOException e) {
            System.err.println("Error processing files: " + e.getMessage());
            e.printStackTrace();
        }
    }

    public static void convertDeleteToInsert(String inputFile, String outputFile) throws IOException {
        // 匹配DELETE语句块的正则表达式
        Pattern deletePattern = Pattern.compile(
                "### DELETE FROM (\\S+?)\\s*### WHERE([\\s\\S]*?)(?=\\n### DELETE|\\n### INSERT|\\n### UPDATE|\\n# at|\\z)",
                Pattern.DOTALL
        );

        try (BufferedReader reader = new BufferedReader(new FileReader(inputFile));
             BufferedWriter writer = new BufferedWriter(new FileWriter(outputFile))) {

            StringBuilder content = new StringBuilder();
            String line;

            // 读取整个文件内容
            while ((line = reader.readLine()) != null) {
                content.append(line).append("\n");
            }

            String fileContent = content.toString();
            Matcher matcher = deletePattern.matcher(fileContent);

            StringBuffer result = new StringBuffer();
            int lastEnd = 0;

            while (matcher.find()) {
                // 保留匹配之前的原始内容
                result.append(fileContent, lastEnd, matcher.start());
                lastEnd = matcher.end();

                String tableName = matcher.group(1).trim();
                String whereClause = matcher.group(2).trim();

                // 处理 WHERE 子句中的列值对
                String insertStatement = buildInsertFromDelete(tableName, whereClause);
                result.append(insertStatement);
            }

            // 添加剩余内容
            result.append(fileContent.substring(lastEnd));

            writer.write(result.toString());
        }
    }

    private static String buildInsertFromDelete(String tableName, String whereClause) {
        List<String> values = new ArrayList<>();

        // 按行分割WHERE子句
        String[] lines = whereClause.split("\n");

        for (String line : lines) {
            line = line.trim();
            if (line.startsWith("###")) {
                line = line.substring(3).trim(); // 移除前导的###
            }

            // 提取 @数字=值 格式的字段
            if (line.startsWith("@")) {
                int equalsIndex = line.indexOf('=');
                if (equalsIndex > 0) {
                    String value = line.substring(equalsIndex + 1).trim();
                    values.add(value);
                }
            }
        }

        // 构建 INSERT 语句（使用列位置而不是列名）
        if (!values.isEmpty()) {
            StringBuilder columns = new StringBuilder();
            StringBuilder valueList = new StringBuilder();

            // 生成列名 @1, @2, @3...
            for (int i = 0; i < values.size(); i++) {
                if (i > 0) {
                    columns.append(", ");
                    valueList.append(", ");
                }
                columns.append("`@").append(i + 1).append("`");
                valueList.append(values.get(i));
            }

            return String.format("INSERT INTO %s (%s) VALUES (%s);\n",
                    tableName, columns.toString(), valueList.toString());
        } else {
            // 如果没有提取到列，返回空字符串
            return "";
        }
    }
}
