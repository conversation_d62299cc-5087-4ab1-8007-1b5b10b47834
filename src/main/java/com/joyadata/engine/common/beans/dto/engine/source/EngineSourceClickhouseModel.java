package com.joyadata.engine.common.beans.dto.engine.source;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.LinkedHashMap;

/**
 * <AUTHOR>
 * @date 2024/2/21
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class EngineSourceClickhouseModel extends EngineBaseSourceModel implements Serializable {
    /**
     * 主机
     */
    private String host;

    /**
     * The ClickHouse database.
     */
    private String database;

    /**
     * 源端jdbc 账号
     */
    private String username;
    /**
     * 源端jdbc 密码
     */
    private String password;
    /**
     * 查询语句  根据输入表自定义选择内容后拼接的sql语句，例如select id,nam from user where id>10
     */
    private String sql;

    /**
     * 等待数据库操作完成以验证连接的时间(秒)
     */
    private String connectionCheckTimeoutSec = "30";
    /**
     * 并行分区的列名，仅支持数字类型。
     * 并行读取拆分使用的字段
     */
    private String partitionColumn;

    //分片数
    /**
     * 当设置分片数后，partitionColumn必须设置
     */
    private String partitionNum;

    /**
     * 表快照的分割大小（行数），读取表快照时，捕获的表被分割为多个分割
     */
    private String snapshotSplitSize = "8096";
    /**
     * 读取表快照时每次轮询的最大提取大小
     */
    private String snapshotFetchSize = "1024";
    /**
     * 块键分布因子的下界。分布因子用于确定表是否均匀分布。当数据分布均匀时，表块将使用均匀计算优化，当数据分布不均匀时，会发生查询拆分。
     * 分布因子可以通过（MAX（id）- MIN（id）+1）/ rowCount计算得出
     */
    private String chunkKeyEvenDistributionFactorLowerBound = "0.05";
    /**
     * 块键分布因子的上界。分布因子用于确定表是否均匀分布。当数据分布均匀时，表块将使用均匀计算优化，当数据分布不均匀时，会发生查询拆分。
     * 分布因子可以通过（MAX（id）- MIN（id）+1）/ rowCount计算得出
     */
    private String chunkKeyEvenDistributionFactorUpperBound = "100";
    /**
     * 这是指触发样本分片策略的估计分片数量的阈值。当分布因子超出上下界时，如果估计的分片数量（approximateRowCnt/chunkSize）超过此阈值，
     * 将使用样本分片策略。这种策略可以帮助更有效地处理大型数据集。默认值为1000个分片。
     */
    private String sampleShardingThreshold = "1000";
    /**
     * 样本分片策略的抽样率的倒数。该值表示抽样率分数的分母。例如，值为1000表示抽样率为1/1000。当触发样本分片策略时使用此参数。
     */
    private String inverseSamplingRate = "1000";

    /**
     * jdbcurl后的参数
     */
    private LinkedHashMap<String, String> optionsConfig;

    /**
     * 通道数，对应engine.ftl中的key table_path
     */
    private Integer pipeline;
}
