package com.joyadata.engine.common.beans.utils.dialect.converter;

import com.joyadata.engine.common.beans.dto.DdlDTO;
import com.joyadata.engine.common.beans.dto.datasource.MetadataColumnDTO;
import com.joyadata.engine.common.beans.utils.dialect.AbstractDatabaseTypeConverter;
import com.joyadata.engine.common.beans.utils.dialect.paramters.DDLParameters;
import groovy.lang.Tuple2;
import groovy.lang.Tuple3;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.sql.Types;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2025/4/27 17:50
 */
@Slf4j
public class StarrocksTypeConverter extends AbstractDatabaseTypeConverter {
    @Override
    protected Tuple3<String, String, String> doGenerateDDL(DDLParameters ddlParameters) {
        String schema = ddlParameters.getSchema();
        String tableName = ddlParameters.getTableName();
        List<DdlDTO> columns = ddlParameters.getColumns();
        String tableCnName = ddlParameters.getTableCnName();
        String fieldUppLower = ddlParameters.getFieldUppLower();
        if (StringUtils.isNotBlank(fieldUppLower)) {
            tableName = getUppOrLowerCase(fieldUppLower, tableName);
        }
        String checkTableSql = String.format("select 1 from %s where 1=2", tablePath(schema, tableName));
        String create = String.format("create table %s (", tablePath(schema, tableName));
        StringBuilder ddl = new StringBuilder(create);
        columns.forEach(ddlDTO -> {
            int dataType = ddlDTO.getDataTypeJava();
            int columnSize = ddlDTO.getColumnSize();
            int decimalDigits = ddlDTO.getDecimalDigits();
            String dataName = ddlDTO.getDataType();
            String columnName;
            if (StringUtils.isNotBlank(fieldUppLower)) {
                columnName = getUppOrLowerCase(fieldUppLower, ddlDTO.getColumnName());
            } else {
                columnName = ddlDTO.getColumnName().toLowerCase();
            }
            String cnName = ddlDTO.getColumnCnName();
            String dataTypeString = getStarRocksTypeFromSqlType(dataType, dataName, columnSize, decimalDigits);
            if (StringUtils.isNotBlank(cnName)) {
                cnName = cnName.replaceAll("'", "`");
                cnName = cnName.replaceAll("\"", "“");
                ddl.append("`").append(columnName).append("` ").append(dataTypeString).append(" COMMENT '").append(cnName).append("'").append(",");
            } else {
                ddl.append("`").append(columnName).append("` ").append(dataTypeString).append(",");
            }
        });
        List<DdlDTO> pkList = columns.stream().filter(ddlDTO -> ddlDTO.getPrimaryKey() == 1).collect(Collectors.toList());
        String ddlTmp = ddl.toString();
        ddlTmp = ddlTmp.substring(0, ddlTmp.length() - 1);
        StringBuilder ddlRes = new StringBuilder(ddlTmp);
        ddlRes.append(")");
        ddlRes.append(" ENGINE=OLAP ");
        if (!pkList.isEmpty()) {
            String pkFields = getPkSql(pkList);
            ddlRes.append(" UNIQUE KEY (").append(pkFields).append(") ");
            ddlRes.append(" DISTRIBUTED BY HASH(").append(pkFields).append(") BUCKETS 1 PROPERTIES (\"replication_num\" = \"1\")");
        }
        String result = ddlRes.toString();
        log.info("StarRocksFromMetadata生成StarRocks建表语句是 {}", result);
        log.info("StarRocksFromMetadata生成StarRocks检测表是否存在的语句是 {}", checkTableSql);
        Tuple3<String, String, String> tuple = new Tuple3(result, "", checkTableSql);
        return tuple;
    }

    private static String getStarRocksTypeFromSqlType(int dataType, String dataName, int columnSize, int decimalDigits) {
        switch (dataType) {
            case Types.BOOLEAN:
                return "BOOLEAN";
            case Types.BIT:
            case Types.TINYINT:
                return "TINYINT";
            case Types.SMALLINT:
                return "SMALLINT";
            case Types.INTEGER:
                return "INT";
            case Types.BIGINT:
                return "BIGINT";
            case Types.FLOAT:
                return "FLOAT";
            case Types.REAL:
            case Types.DOUBLE:
                return "DOUBLE";
            case Types.NUMERIC:
            case Types.DECIMAL:
                if (decimalDigits == 0) {
                    if (columnSize == 1) {
                        return "TINYINT";
                    } else if (columnSize < 3) {
                        return "SMALLINT";
                    } else if (columnSize < 5) {
                        return "MEDIUMINT";
                    } else if (columnSize < 10) {
                        return "INT";
                    } else if (columnSize < 19) {
                        return "BIGINT";
                    } else if (columnSize > 65) {
                        return "DECIMAL( 65 )";
                    } else {
                        return "DECIMAL(" + columnSize + ")";
                    }
                } else if (decimalDigits == -127) {
                    if (columnSize == 0 || columnSize > 65) {
                        return "DECIMAL(" + 65 + ", " + 0 + ")";
                    } else {
                        return "DECIMAL(" + columnSize + ", " + 0 + ")";
                    }
                } else {
                    if ("float4".equalsIgnoreCase(dataName) || "float8".equalsIgnoreCase(dataName)) {//pg字段处理
                        return "DOUBLE";
                    }
                    return "DECIMAL(" + columnSize + ", " + decimalDigits + ")";
                }
            case Types.DATE:
                return "DATE";
            case Types.TIME:
                return "TIME";
            case Types.TIMESTAMP:
                return "DATETIME";
            case Types.BINARY:
            case Types.VARBINARY:
            case Types.LONGVARBINARY:
            case Types.CHAR:
            case Types.VARCHAR:
            case Types.NCHAR:
            case Types.NVARCHAR:
            case Types.LONGNVARCHAR:
            case Types.LONGVARCHAR:
                if (columnSize > 0 && columnSize <= 255) {
                    return "VARCHAR(" + columnSize + ")";
                } else {
                    return "TEXT";
                }
            case Types.NCLOB:
            case Types.BLOB:
            case Types.CLOB:
                return "TEXT";
            default:
                if (StringUtils.isNotBlank(dataName)) {
                    if ("NVARCHAR2".equalsIgnoreCase(dataName)) {
                        return "VARCHAR(" + columnSize + ")";
                    } else if ("VARCHAR2".equalsIgnoreCase(dataName)) {
                        return "VARCHAR(" + columnSize + ")";
                    } else if (dataName.startsWith("TIMESTAMP")) {
                        return "DATETIME";
                    } else if ("INTERVAL YEAR(2) TO MONTH".equalsIgnoreCase(dataName)) {
                        return "VARCHAR(10)";
                    } else if (dataName.equalsIgnoreCase("serial")) {//pg数据库转int
                        return "INT";
                    }
                }
                return "TEXT";
        }
    }

    @Override
    public Tuple2<String, List<String>> generateAddPkSql(String schema, String tableName, List<MetadataColumnDTO> pkFieldList, String fieldUppLower) {
        return new Tuple2<>("", null);
    }

    @Override
    public String qute() {
        return "`";
    }

    private String getPkSql(List<DdlDTO> pkList) {
        if (pkList.isEmpty()) {
            return "";
        }
        return pkList.stream().map(dto -> qute() + dto.getColumnName() + qute()).collect(Collectors.joining(","));
    }

}
