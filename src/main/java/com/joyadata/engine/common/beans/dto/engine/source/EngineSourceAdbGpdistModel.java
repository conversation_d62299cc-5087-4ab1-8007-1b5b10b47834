package com.joyadata.engine.common.beans.dto.engine.source;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.LinkedHashMap;

/**
 * <AUTHOR>
 * @date 2024/7/9 14:00
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class EngineSourceAdbGpdistModel implements Serializable {
    private static final long serialVersionUID = 1L;
    private String resultTableName;

    private String path;

    private LinkedHashMap<String, String> schema;

    private String fileFormatType;

    private String schemaName;
    /**
     * 仅当file_format_type为文本时使用
     */
    private String fieldDelimiter;

    private String adbUrl;

    private String adbDriver;

    private String adbUser;

    private String adbPassword;

    private String adbDatabase;

    private String adbTable;

    private LinkedHashMap<String, String> adbSchema;

    private String adbGpfdistDddress;

    private String adbPrefix;

    /**
     * 外表文件路径
     */
    private String adbTmpFilePath;

    /**
     * 外表名称
     */
    private String adbExternalTableName;

    /**
     * gpfdist路径
     */
    private String adbGpfdistPath;

    /**
     * 外表文件列分隔符
     */
    private String adbExternalTableDelimiter;
    /**
     * 外表schema
     */
    private String adbExternalTableSchema;


    /**
     * where条件
     */
    private String adbWhereSql;
}