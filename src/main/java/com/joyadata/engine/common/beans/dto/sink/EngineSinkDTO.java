package com.joyadata.engine.common.beans.dto.sink;


import com.joyadata.engine.common.beans.enums.EngineSinkTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version V1.0
 * @ClassName: EngineSinkDTO
 * @date 2024/2/21
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class EngineSinkDTO implements Serializable {
    /**
     * 浙江农信需求，分区抽取tdsql数据落到文件，文件名中需要有唯一标识否则多读多写会覆盖文件，将每一组读写的分区号传去st，放进文件名中
     */
    private String tdsqlPartitionName;
    /**
     * 类型
     */
    private EngineSinkTypeEnum type;
    /**
     * 组件名称
     */
    private String configName;
    /**
     * 源端配置的 resultTableName
     */
    private String sourceTableName;

    private EngineSinkJDBCDTO jdbcSink;

    private EngineSinkClickHouseDTO clickHouseSink;

    private EngineSinkKafkaDTO kafkaSink;

    private EngineSinkHiveDTO hiveSink;

    private EngineSinkHdfsFileDTO hdfsFileSink;

    private EngineSinkMongoDBDTO mongodbSink;

    private EngineSinkKuduDTO kuduSink;

    private EngineSinkLocalFileDTO localFileSink;

    private EngineSinkSFTPDTO sftpFileSink;

    private EngineSinkFTPDTO ftpFileSink;

    private EngineSinkOssAliFileDTO ossAliFileSink;

    private EngineSinkS3FileDTO s3FileSink;

    private EngineSinkDorisDTO dorisSink;

    private EngineSinkOssHuaweiFilDTO ossHuaweiFileSink;

    private EngineSinkDataHubDTO dataHubSink;//datahub相关配置

    /**
     * argo HDFS FILE 配置
     */
    private EngineSinkArgoHdfsFileDTO argoHdfsFileSink;

    private EngineSinkElasticsearchDTO elasticsearchSink;

    private EngineSinkHbaseDTO hbaseSink;

    private EngineSinkLocalFile2FileDTO localFile2FileSink;
    private EngineSinkOssAliFile2FileDTO ossAliFile2FileSink;
    private EngineSinkS3File2FileDTO s3File2FileSink;
    private EngineSinkSFTP2FileDTO sftp2FileSink;
    private EngineSinkInspurOSSFile2FileDTO inspurOSSFile2FileSink;

    /**
     * adb gpdist 目标端配置
     */
    private EngineSinkAdbGpdistDTO adbGpdistSink;

    private EngineSinkDWSPGDTO dwspgSink;

    private EngineHttpBaseSinkDTO httpBaseSink;
    private EngineMaxComputeSinkDTO maxComputeSink;
}
