package com.joyadata.engine.common.beans.utils.dialect.converter;

import com.alibaba.fastjson.JSON;
import com.joyadata.engine.common.beans.dto.DdlDTO;
import com.joyadata.engine.common.beans.dto.datasource.MetadataColumnDTO;
import com.joyadata.engine.common.beans.utils.dialect.AbstractDatabaseTypeConverter;
import com.joyadata.engine.common.beans.utils.dialect.paramters.DDLParameters;
import groovy.lang.Tuple2;
import groovy.lang.Tuple3;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.sql.Types;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/4/27 17:50
 */
@Slf4j
public class TbaseTypeConverter extends AbstractDatabaseTypeConverter {
    @Override
    protected Tuple3<String, String, String> doGenerateDDL(DDLParameters ddlParameters) {
        String schema = ddlParameters.getSchema();
        String tableName = ddlParameters.getTableName();
        List<DdlDTO> columns = ddlParameters.getColumns();
        String tableCnName = ddlParameters.getTableCnName();
        String fieldUppLower = ddlParameters.getFieldUppLower();
        if (StringUtils.isNotBlank(fieldUppLower)) {
            tableName = getUppOrLowerCase(fieldUppLower, tableName);
        }
        List<String> list = new ArrayList<>();
        String create = "create table " + tableName + " (";
        String checkTableSql = "select 1 from " + tableName + " where 1=2";
        if (StringUtils.isNotBlank(schema)) {
            if (StringUtils.isNotBlank(fieldUppLower)) {
                schema = getUppOrLowerCase(fieldUppLower, schema);
            }
            create = "create table " + schema + "." + tableName + " (";
            checkTableSql = "select 1 from " + schema + "." + tableName + " where 1=2";
        }
        StringBuilder ddl = new StringBuilder(create);
        String finalTableName = tableName;
        String finalSchema = schema;
        columns.forEach(ddlDTO -> {
            int dataType = ddlDTO.getDataTypeJava();// Integer.valueOf(column.get("data_type"));
            int columnSize = ddlDTO.getColumnSize();// Integer.valueOf(column.get("max_length"));
            int decimalDigits = ddlDTO.getDecimalDigits(); //Integer.valueOf(column.get("decimal_digits"));
            String dataName = ddlDTO.getDataType(); //column.get("type_name");
            String columnName;
            if (StringUtils.isNotBlank(fieldUppLower)) {
                columnName = getUppOrLowerCase(fieldUppLower, ddlDTO.getColumnName());
            } else {
                columnName = ddlDTO.getColumnName().toLowerCase();
            }
            String cnName = ddlDTO.getColumnCnName();// StringUtils.defaultString(column.get("remarks"),"");
            String dataTypeString = getTbaseFromSqlType(dataType, dataName, columnSize, decimalDigits);
            ddl.append("\"").append(columnName).append("\" ").append(dataTypeString).append(",");
            if (StringUtils.isNotBlank(cnName)) {
                cnName = cnName.replaceAll("'", "`");
                cnName = cnName.replaceAll("\"", "“");
                if (StringUtils.isNotBlank(finalSchema)) {
                    list.add("COMMENT ON COLUMN " + finalSchema + "." + finalTableName + "." + columnName + " IS '" + cnName + "'");
                } else {
                    list.add("COMMENT ON COLUMN " + finalTableName + "." + columnName + " IS '" + cnName + "'");
                }
            }
        });
        //表中文名
        if (StringUtils.isNotBlank(tableCnName)) {
            tableCnName = tableCnName.replaceAll("'", "`");
            tableCnName = tableCnName.replaceAll("\"", "“");
            if (StringUtils.isNotBlank(schema)) {
                list.add("COMMENT ON TABLE  " + schema + "." + tableName + " IS '" + tableCnName + "'");
            } else {
                list.add("COMMENT ON TABLE  " + tableName + " IS '" + tableCnName + "'");
            }
        }
        String result = ddl.toString();
        result = result.substring(0, result.length() - 1);
        result = result + ")";
        log.info("TbaseFromMetadata生成Tbase建表语句是 {}", result);
        log.info("TbaseFromMetadata生成Tbase检测表是否存在的语句是 {}", checkTableSql);
        Tuple3<String, String, String> tuple2 = new Tuple3<>(result, JSON.toJSONString(list), checkTableSql);
        return tuple2;
    }

    private static String getTbaseFromSqlType(int dataType, String dataName, int columnSize, int decimalDigits) {
        switch (dataType) {
            case Types.VARCHAR:
            case Types.CHAR:
                if (columnSize > 4000) {
                    return "TEXT";
//                    return "VARCHAR(" + columnSize + ")";
                } else if (columnSize == 0) {
                    return "TEXT";
                } else {
                    int changeLeng = columnSize * 3;
//                    return "VARCHAR(" + changeLeng + ")";
                    if (changeLeng >= 4000) {
                        return "TEXT";
                    } else {
                        return "VARCHAR(" + changeLeng + ")";
                    }
                }
            case Types.TINYINT:
                return "SMALLINT";
            case Types.SMALLINT:
            case Types.BIT:
            case Types.INTEGER:
                return "INTEGER";
            case Types.BIGINT:
                return "BIGINT";
            case Types.FLOAT:
            case Types.REAL:
                /*if ("float4".equalsIgnoreCase(dataName)) {
                    return "float4";
                }*/
                return "float4";
            //return "NUMERIC(" + columnSize + "," + decimalDigits + ")";
            case Types.DOUBLE:
                /*if ("float8".equalsIgnoreCase(dataName)) {
                    return "float8";
                }
                return "NUMERIC(" + columnSize + "," + decimalDigits + ")";*/
                return "float8";
            case Types.DECIMAL:
            case Types.NUMERIC:
                if (columnSize == 0 && decimalDigits == -127) {
                    return "NUMERIC(38,0)";
                } else {
                    return "NUMERIC(" + columnSize + "," + decimalDigits + ")";
                }
            case Types.BOOLEAN:
                return "BOOLEAN";
            case Types.DATE:
                return "DATE";
            case Types.TIME:
                return "TIME";
            case Types.TIMESTAMP:
                return "TIMESTAMP";
            case Types.BLOB:
                return "bytea";
            case Types.CLOB:
            case Types.LONGVARCHAR:
                return "TEXT";
            default:
                if (StringUtils.isNotBlank(dataName)) {
                    if ("NVARCHAR2".equalsIgnoreCase(dataName)) {
                        return "VARCHAR(" + columnSize + ")";
                    } else if ("VARCHAR2".equalsIgnoreCase(dataName)) {
                        int changeLeng = columnSize * 3;
                        if (changeLeng >= 4000 || changeLeng == 0) {
                            return "TEXT";
                        } else {
                            return "VARCHAR(" + changeLeng + ")";
                        }
                    } else if (dataName.startsWith("TIMESTAMP")) {
                        return "TIMESTAMP";
                    } else if ("binary".equalsIgnoreCase(dataName) || "varbinary".equalsIgnoreCase(dataName)
                            || "image".equalsIgnoreCase(dataName) || "timestamp".equalsIgnoreCase(dataName) || "bytea".equalsIgnoreCase(dataName)) {//解决mysql到pg问题20230427,sqlserver 也是这个数据类型
                        return "bytea";
                    } else if ("bpchar".equalsIgnoreCase(dataName)) {//pg特殊处理
                        return "bpchar(" + columnSize + ")";
                    } else if ("float4".equalsIgnoreCase(dataName) || "float8".equalsIgnoreCase(dataName)
                            || "interval".equalsIgnoreCase(dataName) || "point".equalsIgnoreCase(dataName)
                            || "line".equalsIgnoreCase(dataName) || "lseg".equalsIgnoreCase(dataName)
                            || "box".equalsIgnoreCase(dataName) || "circle".equalsIgnoreCase(dataName)
                            || "inet".equalsIgnoreCase(dataName) || "cidr".equalsIgnoreCase(dataName)
                            || "json".equalsIgnoreCase(dataName) || "xml".equalsIgnoreCase(dataName)
                            || "uuid".equalsIgnoreCase(dataName) || "_int4".equalsIgnoreCase(dataName)
                            || "_text".equalsIgnoreCase(dataName) || "int4range".equalsIgnoreCase(dataName)
                            || "tsvector".equalsIgnoreCase(dataName) || "tsquery".equalsIgnoreCase(dataName)
                            || "int4".equalsIgnoreCase(dataName) || "int8".equalsIgnoreCase(dataName)
                            || "int2".equalsIgnoreCase(dataName) || "serial".equals(dataName)) {//pg特殊处理
                        return dataName;
                    } else if ("NCLOB".equalsIgnoreCase(dataName) || "BINARY_FLOAT".equalsIgnoreCase(dataName) || "BINARY_DOUBLE".equalsIgnoreCase(dataName)) {
                        return "TEXT";
                    } else if ("INTERVAL YEAR(2) TO MONTH".equalsIgnoreCase(dataName)) {
                        return "VARCHAR(10)";
                    } else if ("BFILE".equalsIgnoreCase(dataName)) {
                        return "TEXT";
                    } else if (dataName.startsWith("TIMESTAMP")) {
                        return "TIMESTAMP";
                    }
                }
                return "VARCHAR(4000)";
        }
    }

    @Override
    public Tuple2<String, List<String>> generateAddPkSql(String schema, String tableName, List<MetadataColumnDTO> pkFieldList, String fieldUppLower) {
        return new Tuple2<>("", null);
    }
}
