package com.joyadata.engine.common.beans.dto.sink;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/3/1
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class EngineSinkKafkaDTO implements Serializable {
    /**
     * 数据源id
     */
    private String datasourceInfoId;

    /**
     * kafka主题
     */
    private String topic;

    /**
     * 请求超时时间（毫秒）
     */
    private String kafkaRequestTimeoutMs;
    /**
     * 数据格式
     */
    private String format;
    /**
     * 语义。如 AT_LEAST_ONCE
     */
    private String semantics;
    /**
     * 字段分隔符
     */
    private String fieldDelimiter;
    /**
     * 分区
     */
    private String partition;
    /**
     * kafka配置，如：
     * {
     * acks = "all"
     * request.timeout.ms = 60000
     * buffer.memory = 33554432
     * }
     */
    private Map<String, String> kafkaConfig;


}
