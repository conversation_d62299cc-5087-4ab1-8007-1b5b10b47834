package com.joyadata.engine.common.beans.dto;

import com.joyadata.engine.common.beans.dto.datasource.MetadataColumnDTO;
import com.joyadata.engine.common.beans.enums.DatabaseTypeEnum;
import com.joyadata.engine.common.beans.enums.HiveStorageTypeEnum;
import com.joyadata.engine.common.beans.enums.PrimaryKeyModelEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class CreateTableDTO {
    //数据库类型
    private DatabaseTypeEnum type;
    private String schema;
    private String tableName;
    private List<MetadataColumnDTO> fieldList;
    /**
     * 表中文名
     */
    private String tableCnName;
    /**
     * hive建表类型
     */
    private HiveStorageTypeEnum hiveStorageType;
    /**
     * 转大小写
     */
    private String fieldUppLower;
    /**
     * DORIS 建表模型
     */
    private PrimaryKeyModelEnum primaryKeyModelEnum = PrimaryKeyModelEnum.UNIQUE;

    /**
     * 优先拿此字段值，如果此字段值为空，则取元数据中的主键信息
     * 默认是支持目标端数据更新的，当此字段不为空时
     * 主键更新，多个主键["a","b"]
     */
    private List<String> primaryKeyList;
    
}
