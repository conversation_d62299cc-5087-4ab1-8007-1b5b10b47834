package com.joyadata.engine.common.beans.enums;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2021/11/26.
 */
public enum MySQLColumnTypeEnum {
    /**
     * 整形
     */
    TINYINT, SMALLINT, MEDIUMINT, INT, BIGINT,
    /**
     * 浮点型
     */
    DECIMAL, FLOAT, DOUBLE, REAL,
    /**
     * 时间类型
     */
    DATETIME, DATE, TIME, TIMESTAMP, YEAR,
    /**
     * 字符串类型
     */
    CHAR, VARCHAR, TINYTEXT, TEXT, MEDIUMTEXT, LONGTEXT,
    /**
     * 二进制大文件类型
     */
    BINARY, VARBINARY, TINYBLOB, BLOB, MEDIUMBLOB, LONGBLOB
}
