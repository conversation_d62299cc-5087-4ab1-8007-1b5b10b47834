package com.joyadata.engine.common.beans.dto.sink;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class EngineSinkLocalFile2FileDTO {
    /**
     * 目标文件路径
     */
    private String path;
    /**
     * 校验文件
     */
    private String validateFile;
    /**
     * 校验内容
     */
    private String validateContent;
}
