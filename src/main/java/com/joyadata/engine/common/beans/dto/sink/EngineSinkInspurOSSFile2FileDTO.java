package com.joyadata.engine.common.beans.dto.sink;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class EngineSinkInspurOSSFile2FileDTO {
    /**
     * 数据源id
     */
    private String datasourceInfoId;
    /**
     * 源文件路径
     */
    private String path;
    /**
     * 校验文件
     */
    private String validateFile;
    /**
     * 校验内容
     */
    private String validateContent;

    private String fsS3aAwsCredentialsProvider;

    /**
     * 数据加载方式默认S3
     * S3
     * ICFSDOS
     */
    private String loadType = "S3";
}
