package com.joyadata.engine.common.beans.utils.dialect.paramters;

import com.joyadata.engine.common.beans.enums.PrimaryKeyModelEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/4/29 15:20
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class DorisDDLParameters extends DDLParameters{
    private PrimaryKeyModelEnum primaryKeyModelEnum;
    private List<String> primaryKeyList;

}
