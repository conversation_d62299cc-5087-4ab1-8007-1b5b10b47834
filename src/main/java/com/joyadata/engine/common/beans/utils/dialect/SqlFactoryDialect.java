package com.joyadata.engine.common.beans.utils.dialect;

import com.joyadata.engine.common.beans.dto.CreateTableDTO;
import com.joyadata.engine.common.beans.dto.DdlDTO;
import com.joyadata.engine.common.beans.dto.datasource.MetadataColumnDTO;
import com.joyadata.engine.common.beans.utils.dialect.paramters.DDLParameters;
import com.joyadata.engine.common.beans.utils.dialect.paramters.DorisDDLParameters;
import com.joyadata.engine.common.beans.utils.dialect.paramters.HiveDDLParameters;
import groovy.lang.Tuple2;
import groovy.lang.Tuple3;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
public class SqlFactoryDialect {
    public static Tuple3<String, String, String> generateDDL(CreateTableDTO createTableDTO) throws Exception {
        String databaseType = createTableDTO.getType().getName();
        String schema = createTableDTO.getSchema();
        String tableName = createTableDTO.getTableName();
        List<MetadataColumnDTO> fieldList = createTableDTO.getFieldList();
        List<MetadataColumnDTO> columnDTOList = fieldList.stream().sorted(Comparator.comparingLong(MetadataColumnDTO::getColumnSort)).collect(Collectors.toList());
        List<DdlDTO> columns = initDdlDTOs(columnDTOList);
        String tableCnName = createTableDTO.getTableCnName();
        String fieldUppLower = createTableDTO.getFieldUppLower();

        try {
            log.info("Generating DDL for database type: {}, schema: {}, table: {}", databaseType, schema, tableName);
            DatabaseTypeConverter converter = DatabaseTypeConverterFactory.getConverter(databaseType);
            DDLParameters ddlParameters;
            if ("hive".equalsIgnoreCase(databaseType) || "inceptor".equalsIgnoreCase(databaseType)) {
                ddlParameters = HiveDDLParameters.builder()
                        .hiveStorageType(createTableDTO.getHiveStorageType())
                        .schema(schema)
                        .tableName(tableName)
                        .columns(columns)
                        .tableCnName(tableCnName)
                        .fieldUppLower(fieldUppLower)
                        .build();
            } else if ("doris".equalsIgnoreCase(databaseType)) {
                //取主键字段
                List<String> primaryKeyList = columnDTOList.stream().filter(m -> "1".equals(m.getColumnPrimaryKey())).map(MetadataColumnDTO::getColumnName).collect(Collectors.toList());
                if (null != createTableDTO.getPrimaryKeyList() && !createTableDTO.getPrimaryKeyList().isEmpty()) {
                    primaryKeyList = createTableDTO.getPrimaryKeyList();
                }
                if (primaryKeyList.isEmpty()) {
                    throw new Exception("Doris Primary key list is empty");
                }
                ddlParameters = DorisDDLParameters.builder()
                        .primaryKeyModelEnum(createTableDTO.getPrimaryKeyModelEnum())
                        .primaryKeyList(primaryKeyList)
                        .schema(schema)
                        .tableName(tableName)
                        .columns(columns)
                        .tableCnName(tableCnName)
                        .fieldUppLower(fieldUppLower)
                        .build();
            } else {
                ddlParameters = DDLParameters.builder()
                        .schema(schema)
                        .tableName(tableName)
                        .columns(columns)
                        .tableCnName(tableCnName)
                        .fieldUppLower(fieldUppLower).build();
            }
            Tuple3<String, String, String> result = converter.generateDDL(ddlParameters);
            log.debug("Generated DDL: {}", result.getFirst());
            return result;
        } catch (Exception e) {
            log.error("Failed to generate DDL for database type: {}", databaseType, e);
            throw new Exception("Failed to generate DDL DDL_GENERATION_FAILED", e);
        }
    }

    public static Tuple2<String, List<String>> generateAddPkSql(String databaseType, String schema, String tableName,
                                                                List<MetadataColumnDTO> pkFieldList, String fieldUppLower) throws Exception {
        try {
            log.info("Generating add PK SQL for database type: {}, schema: {}, table: {}", databaseType, schema, tableName);
            DatabaseTypeConverter converter = DatabaseTypeConverterFactory.getConverter(databaseType);
            Tuple2<String, List<String>> result = converter.generateAddPkSql(schema, tableName, pkFieldList, fieldUppLower);
            log.debug("Generated add PK SQL: {}", result.getFirst());
            return result;
        } catch (Exception e) {
            log.error("Failed to generate add PK SQL for database type: {}", databaseType, e);
            throw new Exception("Failed to generate add PK SQL PK_SQL_GENERATION_FAILED", e);
        }
    }

    //通过字段信息获取创建DdlDTO集合
    private static List<DdlDTO> initDdlDTOs(List<MetadataColumnDTO> columnDTOList) {
        List<DdlDTO> ddlDTOs = new ArrayList<>();
        columnDTOList.forEach(c -> {
            DdlDTO ddlDTO = new DdlDTO();
            ddlDTO.setColumnName(c.getColumnName());
            ddlDTO.setColumnCnName(c.getColumnCnName());
            ddlDTO.setDataType(c.getColumnType());
            ddlDTO.setDataTypeJava(Integer.parseInt(c.getDataType()));
            ddlDTO.setColumnSize(StringUtils.isBlank(c.getColumnLength()) ? 0 : Integer.parseInt(c.getColumnLength()));
            ddlDTO.setDecimalDigits(StringUtils.isBlank(c.getColumnScale()) ? 0 : Integer.parseInt(c.getColumnScale()));
            ddlDTO.setPosition(Math.toIntExact(c.getColumnSort()));
            ddlDTO.setPrimaryKey(StringUtils.isBlank(c.getColumnPrimaryKey()) ? 0 : Integer.parseInt(c.getColumnPrimaryKey()));
            ddlDTO.setPartition(StringUtils.isBlank(c.getPartitionColumn()) ? 0 : Integer.parseInt(c.getPartitionColumn()));
            ddlDTOs.add(ddlDTO);
        });
        return ddlDTOs;
    }
}
