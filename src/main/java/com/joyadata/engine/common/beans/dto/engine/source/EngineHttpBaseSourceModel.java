package com.joyadata.engine.common.beans.dto.engine.source;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.*;
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class EngineHttpBaseSourceModel implements Serializable {

    private String url;

    private String method = "POST";


    private String format = "json";
    /**
     * http SOURCE 属性
     */
    Map<String, String> params = new HashMap<>();
    Map<String, String> headers = new HashMap<>();
    Map<String, Object> bodys = new HashMap<>();
    String body;
    //分页参数
    Map<String, Object> pageing = new HashMap<>();
    List<LinkedHashMap<String, String>> sourceOutputColumns = new ArrayList<>();


    /**
     * 存储字段---类型映射
     */
    Map<String, Object> fieldTypeMap = new HashMap<>();

    /**
     * 存储字段--jsonPath的映射
     */
    Map<String, Object> fieldJsonPathMap = new HashMap<>();

    /**
     * 发送方式1： form-data
     * 发送方式2： x-www-form-urlencoded
     * 发送方式3： raw
     */
    private String bodySendType = "raw";
    private int connectTimeoutMs = 15;


    private String datetimeFormat = "yyyy-MM-dd HH:mm:ss";

    /**
     * YYYY_MM_DD("yyyy-MM-dd"),
     * YY_MM_DD("yy-MM-dd"),
     * YYYY_MM_DD_SPOT("yyyy.MM.dd"),
     * YYYY_MM_DD_SLASH("yyyy/MM/dd");
     */
    private String dateFormat = "yyyy-MM-dd";
    /**
     * HH_MM_SS("HH:mm:ss"),
     * HH_MM_SS_SSS("HH:mm:ss.SSS");
     */
    private String timeFormat = "HH:mm:ss";


    private String resultTableName;

}
