package com.joyadata.engine.common.beans.utils.dialect.converter;

import com.joyadata.engine.common.beans.dto.DdlDTO;
import com.joyadata.engine.common.beans.dto.datasource.MetadataColumnDTO;
import com.joyadata.engine.common.beans.utils.dialect.AbstractDatabaseTypeConverter;
import com.joyadata.engine.common.beans.utils.dialect.paramters.DDLParameters;
import groovy.lang.Tuple2;
import groovy.lang.Tuple3;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.sql.Types;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2025/4/27 17:50
 */
@Slf4j
public class TdsqlForMysqlTypeConverter extends AbstractDatabaseTypeConverter {
    @Override
    protected Tuple3<String, String, String> doGenerateDDL(DDLParameters ddlParameters) {
        String schema = ddlParameters.getSchema();
        String tableName = ddlParameters.getTableName();
        List<DdlDTO> columns = ddlParameters.getColumns();
        String tableCnName = ddlParameters.getTableCnName();
        String fieldUppLower = ddlParameters.getFieldUppLower();
        if (StringUtils.isNotBlank(fieldUppLower)) {
            tableName = getUppOrLowerCase(fieldUppLower, tableName);
        }
        String create = "create table `" + tableName + "` (";
        String checkTableSql = "select 1 from `" + tableName + "` where 1=2";
        if (StringUtils.isNotBlank(schema)) {
            if (StringUtils.isNotBlank(fieldUppLower)) {
                schema = getUppOrLowerCase(fieldUppLower, schema);
            }
            create = "create table `" + schema + "`.`" + tableName + "` (";
            checkTableSql = "select 1 from `" + schema + "`.`" + tableName + "` where 1=2";
        }
        StringBuilder ddl = new StringBuilder(create);
        columns.forEach(ddlDTO -> {
            int dataType = ddlDTO.getDataTypeJava();// Integer.valueOf(column.get("data_type"));
            int columnSize = ddlDTO.getColumnSize();// Integer.valueOf(column.get("max_length"));
            int decimalDigits = ddlDTO.getDecimalDigits(); //Integer.valueOf(column.get("decimal_digits"));
            String dataName = ddlDTO.getDataType(); //column.get("type_name");
            String columnName;
            if (StringUtils.isNotBlank(fieldUppLower)) {
                columnName = getUppOrLowerCase(fieldUppLower, ddlDTO.getColumnName());
            } else {
                columnName = ddlDTO.getColumnName().toLowerCase();
            }
            String cnName = ddlDTO.getColumnCnName();// StringUtils.defaultString(column.get("remarks"),"");
            String dataTypeString = getMySQLTypeFromSqlType(dataType, dataName, columnSize, decimalDigits);
            if (StringUtils.isNotBlank(cnName)) {
                cnName = cnName.replaceAll("'", "`");
                cnName = cnName.replaceAll("\"", "“");
            }
            ddl.append("`").append(columnName).append("` ").append(dataTypeString).append(" COMMENT '").append(cnName).append("'").append(",");
        });
        String result = ddl.toString();
        List<String> pkList = columns.stream().filter(m -> 1 == (m.getPrimaryKey())).map(DdlDTO::getColumnName).collect(Collectors.toList());
        if (!pkList.isEmpty()) {
            String pkField = pkList.stream()
                    .map(str -> "`" + str + "`") // 在每个元素前后添加双引号
                    .collect(Collectors.joining(","));
            String pkSql = "PRIMARY KEY (" + pkField + ")";
            result = result + pkSql;
        } else {
            result = result.substring(0, result.length() - 1);
        }
        //表中文名
        if (StringUtils.isNotBlank(tableCnName)) {
            tableCnName = tableCnName.replaceAll("'", "`");
            tableCnName = tableCnName.replaceAll("\"", "“");
            result = result + ") COMMENT ='" + tableCnName + "'";
        } else {
            result = result + ")";
        }
        log.info("TdsqlForMySQLFromMetadata生成TdsqlForMySQL建表语句是 {}", result);
        log.info("TdsqlForMySQLFromMetadata生成TdsqlForMySQL检测表是否存在的语句是 {}", checkTableSql);
        Tuple3<String, String, String> tuple = new Tuple3(result, "", checkTableSql);//mysql 备注在sql中
        return tuple;
    }

    public static String getMySQLTypeFromSqlType(int dataType, String dataName, int columnSize, int decimalDigits) {
        switch (dataType) {
            case Types.BIT:
                return "BIT";
            case Types.BOOLEAN:
                return "BOOLEAN";
            case Types.TINYINT:
                return "TINYINT";
            case Types.SMALLINT:
                return "SMALLINT";
            case Types.INTEGER:
                return "INT";
            case Types.BIGINT:
                return "BIGINT";
            case Types.FLOAT:
                if (0 == decimalDigits) {
                    return "FLOAT";
                }
                return "FLOAT(" + columnSize + ", " + decimalDigits + ")";
            case Types.REAL:
                return "REAL";
            case Types.DOUBLE:
                if (0 == decimalDigits) {
                    return "DOUBLE";
                }

                return "DOUBLE(" + columnSize + ", " + decimalDigits + ")";
            case Types.NUMERIC:
            case Types.DECIMAL:
                if (decimalDigits == 0) {
                    if (columnSize == 1) {
                        return "TINYINT";
                    } else if (columnSize < 3) {
                        return "SMALLINT";
                    } else if (columnSize < 5) {
                        return "MEDIUMINT";
                    } else if (columnSize < 10) {
                        return "INT";
                    } else if (columnSize < 19) {
                        return "BIGINT";
                    } else if (columnSize > 65) {
                        return "DECIMAL( 65 )";
                    } else {
                        return "DECIMAL(" + columnSize + ")";
                    }
                } else if (decimalDigits == -127) {
                    if (columnSize == 0 || columnSize > 65) {
                        return "DECIMAL(" + 65 + ", " + 0 + ")";
                    } else {
                        return "DECIMAL(" + columnSize + ", " + 0 + ")";
                    }
                } else {
                    if ("float4".equalsIgnoreCase(dataName) || "float8".equalsIgnoreCase(dataName)) {//pg字段处理
                        return "DOUBLE";
                    }
                    return "DECIMAL(" + columnSize + ", " + decimalDigits + ")";
                }
            case Types.DATE:
                return "DATE";
            case Types.TIME:
                return "TIME";
            case Types.TIMESTAMP:
                return "DATETIME(" + decimalDigits + ")";
            case Types.BINARY:
            case Types.VARBINARY:
            case Types.LONGVARBINARY:
                return "BLOB";
            case Types.CHAR:
            case Types.VARCHAR:
            case Types.NCHAR:
            case Types.NVARCHAR:
                if (columnSize > 0 && columnSize <= 255) {
                    return "VARCHAR(" + columnSize + ")";
                } else {
                    return "TEXT";
                }
            case Types.LONGVARCHAR:
                //* MEDIUMTEXT 的长度是 0 - 16,777,215
                return "MEDIUMTEXT";
            case Types.LONGNVARCHAR:
                //* LONGTEXT 的长度是 0 -  4,294,967,295
                return "LONGTEXT";
            case Types.CLOB:
                return "TEXT";
            case Types.NCLOB:
                return "NCLOB";
            case Types.BLOB:
                return "BLOB";
            default:
                if (StringUtils.isNotBlank(dataName)) {
                    if ("NVARCHAR2".equalsIgnoreCase(dataName)) {
                        return "VARCHAR(" + columnSize + ")";
                    } else if ("VARCHAR2".equalsIgnoreCase(dataName)) {
                        return "VARCHAR(" + columnSize + ")";
                    } else if (dataName.startsWith("TIMESTAMP")) {
                        return "DATETIME";
                    } else if ("INTERVAL YEAR(2) TO MONTH".equalsIgnoreCase(dataName)) {
                        return "VARCHAR(10)";
                    } else if (dataName.startsWith("TIMESTAMP")) {
                        return "DATETIME";
                    } else if (dataName.equalsIgnoreCase("serial")) {//pg数据库转int
                        return "INT";
                    }
                }
                return "TEXT";
        }
    }

    @Override
    public Tuple2<String, List<String>> generateAddPkSql(String schema, String tableName, List<MetadataColumnDTO> pkFieldList, String fieldUppLower) {
        return new Tuple2<>("", null);
    }
}
