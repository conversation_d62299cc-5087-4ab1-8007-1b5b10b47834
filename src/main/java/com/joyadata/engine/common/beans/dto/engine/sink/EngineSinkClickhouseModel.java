package com.joyadata.engine.common.beans.dto.engine.sink;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.LinkedHashMap;

/**
 * <AUTHOR>
 * @date 2024/2/21
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class EngineSinkClickhouseModel implements Serializable {
    /**
     * Clickhouse服务器主机地址
     */
    private String host;

    /**
     * Clickhouse数据库名称
     */
    private String database;

    /**
     * Clickhouse表名
     */
    private String table;

    /**
     * Clickhouse连接用户名
     */
    private String username;

    /**
     * Clickhouse连接密码
     */
    private String password;

    /**
     * Clickhouse表的主键
     */
    private String primaryKey;

    /**
     * 是否支持Upsert操作
     */
    private String supportUpsert;

    /**
     * 当source_table_name未指定时，当前插件处理配置文件中前一个插件输出的dataset数据集。当指定source_table_name时，当前插件处理的是对应此参数的数据集。
     */
    private String sourceTableName;
    /**
     * jdbcurl后的参数
     */
    private LinkedHashMap<String, String> optionsConfig;

}
