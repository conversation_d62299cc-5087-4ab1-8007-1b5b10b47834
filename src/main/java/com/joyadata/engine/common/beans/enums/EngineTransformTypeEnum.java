package com.joyadata.engine.common.beans.enums;

/**
 * <AUTHOR>
 * @date 2024/2/21
 */
public enum EngineTransformTypeEnum {
    <PERSON><PERSON>("Copy"),
    FieldMapper("FieldMapper"),
    FilterRow<PERSON>ind("FilterRowKind"),
    Filter("Filter"),
    Replace("Replace"),
    Split("Split"),
    SQL("SQL"),
    RandomInfo("RandomInfo"),
    ConstantAndNum("ConstantAndNum"),
    CustomDateTime("CustomDateTime"),
    UniqueID("UniqueID"),
    RightJoin("RightJoin"),
    LeftJoin("LeftJoin"),
    InnerJoin("InnerJoin"),
    Dah("Dah"),
    ReplaceAll("ReplaceAll"),
    XmlPath("XmlPath"), // 支持一对多行输出
    XmlPathV2("XmlPathV2");
    private final String name;

    EngineTransformTypeEnum(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }
}
