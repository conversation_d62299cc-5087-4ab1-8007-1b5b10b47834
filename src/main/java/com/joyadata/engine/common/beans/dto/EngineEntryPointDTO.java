package com.joyadata.engine.common.beans.dto;


import com.joyadata.engine.common.beans.dto.engine.EngineEnvMode;
import com.joyadata.engine.common.beans.dto.engine.EngineTransformModel;
import com.joyadata.engine.common.beans.dto.sink.EngineSinkDTO;
import com.joyadata.engine.common.beans.dto.source.EngineSourceDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @ClassName: RawScriptDTO
 * @date 2024/2/21
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class EngineEntryPointDTO {
    private EngineEnvMode env;
    private List<EngineSourceDTO> source;
    private List<EngineSinkDTO> sink;
    private List<EngineTransformModel> transforms;
    /**
     * 其他参数
     */
    private OtherParamsDTO otherParams = new OtherParamsDTO();
}
