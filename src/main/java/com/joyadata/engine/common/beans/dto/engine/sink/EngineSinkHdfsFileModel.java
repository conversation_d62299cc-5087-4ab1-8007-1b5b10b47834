package com.joyadata.engine.common.beans.dto.engine.sink;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/3/18
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class EngineSinkHdfsFileModel implements Serializable {
    /**
     * 当source_table_name未指定时，当前插件处理配置文件中前一个插件输出的dataset数据集。当指定source_table_name时，当前插件处理的是对应此参数的数据集。
     */
    private String sourceTableName;
    /**
     * Hadoop集群地址，以hdfs://开头，例如：hdfs://hadoopcluster
     */
    private String fsDefaultFS;
    /**
     * defunct目标目录路径是必需的。
     */
    private String path;
    /**
     * 结果文件将首先写入一个tmp路径，然后使用mv将tmp目录提交到目标目录。需要一个hdfs路径。
     */
    private String tmpPath;
    /**
     * HDFS-site.xml路径，用于加载名称节点的HA配置
     */
    private String hdfsSitePath;
    /**
     * 是否需要自定义文件名
     */
    private String customFilename;
    /**
     * 仅当custom_filename为true时使用。file_name_expression描述将创建到路径中的文件表达式。我们可以在file_name_expression中添加变量${now}或${uuid}，如test_${uuid}_$｛now｝，$｛now｝表示当前时间，其格式可以通过指定选项filename_time_format来定义。请注意，如果is_enable_transaction为true，我们将自动添加${transactionId}_在文件的头中。
     */
    private String fileNameExpression;
    /**
     * 仅当custom_filename为true时使用。当file_name_expression参数中的格式为xxxx-${now}时，filename_time_format可以指定路径的时间格式，默认值为yyyy。MM.dd。常用的时间格式如下：[y：年，M：月，d：月日，H：天中的小时（0-23），M：小时中的分钟，s：分钟中的秒]
     */
    private String filenameTimeFormat;
    /**
     * 我们支持以下文件类型：text json csv orc parquet excel。请注意，最终文件名将以file_format的后缀结尾，文本文件的后缀为txt。
     */
    private String fileFormatType;
    /**
     * 仅当file_format为文本时使用，数据行中列之间的分隔符。仅文本文件格式需要
     */
    private String fieldDelimiter;
    /**
     * 仅当file_format为文本时使用，文件中行之间的分隔符。仅文本文件格式需要。
     */
    private String rowDelimiter;
    /**
     * 是否需要处理分区
     */
    private String havePartition;
    /**
     * 只有在have_partition为true时才使用，根据所选字段对数据进行分区。
     */
    private String partitionBy;
    /**
     * 仅当have_partition为true时使用，如果指定了partition_by，我们将根据分区信息生成相应的分区目录，最终文件将放置在分区目录中。默认partition_dir_expression为$｛k0｝=$｛v0｝/$｛k1｝=${v1｝//$｛kn｝=$｛vn｝/。k0是第一个分区字段，v0是第一分区字段的值。
     */
    private String partitionDirExpression;
    /**
     * 仅当have_partition为true时使用。如果is_partition_file_write_in_file为true，则分区字段及其值将写入数据文件。例如，如果要编写配置单元数据文件，其值应为false。
     */
    private String isPartitionFieldWriteInFile;
    /**
     * 当此参数为空时，所有字段都是汇点列。哪些列需要写入文件，默认值是从Transform或Source获取的所有列。字段的顺序决定了文件实际写入的顺序。
     */
    private String sinkColumns;
    /**
     * 如果is_enable_transaction为true，我们将确保数据在写入目标目录时不会丢失或重复。请注意，如果is_enable_transaction为true，我们将自动添加${transactionId}_在文件的头中。现在只支持true。
     */
    private String isEnableTransaction;
    /**
     * 文件中的最大行数。对于SeaTunnel Engine，文件中的行数由batch_size和checkpoint.interval共同决定。如果checkpoint.interval的值足够大，则接收器写入程序将在文件中写入行，直到文件中的行大于batch_size。如果checkpoint.interval很小，则当触发新的检查点时，接收器写入程序将创建一个新文件。
     */
    private Integer batchSize;
    /**
     * 文件的压缩编解码器和支持的详细信息如下所示：[txt:lzo-none，json:lzo-nnone，csv:lzo none，orc:lzo-snappy lz4-zlib none，parquet:lzo-ssnappy lz4 gzip brotli zstd none]。提示：excel类型不支持任何压缩格式。
     */
    private String compressCodec;
    /**
     * Kerberos krb5路径
     */
    private String krb5Path;

    /**
     * Kerberos principal
     */
    private String kerberosPrincipal;

    /**
     * Kerberos keytab路径
     */
    private String kerberosKeytabPath;
    /**
     * 仅当file_format为excel时使用。当文件格式为Excel时，可以缓存在内存中的最大数据项数。
     */
    private Integer maxRowsInMemory;

    /**
     * 仅当file_format为excel时使用。编写工作簿的工作表
     */
    private String sheetName;
    /**
     * 空值处理
     */
    private String emptyDataStrategy;
    /**
     * YYYY_MM_DD_HH_MM_SS("yyyy-MM-dd HH:mm:ss"),
     * YYYY_MM_DD_HH_MM_SS_SSSSSS("yyyy-MM-dd HH:mm:ss.SSSSSS"),
     * YYYY_MM_DD_HH_MM_SS_SPOT("yyyy.MM.dd HH:mm:ss"),
     * YYYY_MM_DD_HH_MM_SS_SLASH("yyyy/MM/dd HH:mm:ss"),
     * YYYY_MM_DD_HH_MM_SS_NO_SPLIT("yyyyMMddHHmmss"),
     * YYYY_MM_DD_HH_MM_SS_ISO8601("yyyy-MM-dd'T'HH:mm:ss"),
     * YYYY_MM_DD_HH_MM_SS_SSS_ISO8601("yyyy-MM-dd'T'HH:mm:ss.SSS"),
     * YYYY_MM_DD_HH_MM_SS_SSSSSS_ISO8601("yyyy-MM-dd'T'HH:mm:ss.SSSSSS"),
     * YYYY_MM_DD_HH_MM_SS_SSSSSSSSS_ISO8601("yyyy-MM-dd'T'HH:mm:ss.SSSSSSSSS");
     */
    private String datetimeFormat = "yyyy-MM-dd HH:mm:ss";

    /**
     * YYYY_MM_DD("yyyy-MM-dd"),
     * YY_MM_DD("yy-MM-dd"),
     * YYYY_MM_DD_SPOT("yyyy.MM.dd"),
     * YYYY_MM_DD_SLASH("yyyy/MM/dd");
     */
    private String dateFormat = "yyyy-MM-dd";
    /**
     * HH_MM_SS("HH:mm:ss"),
     * HH_MM_SS_SSS("HH:mm:ss.SSS");
     */
    private String timeFormat = "HH:mm:ss";
}
