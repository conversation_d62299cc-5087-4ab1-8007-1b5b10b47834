package com.joyadata.engine.common.beans.utils.dialect.converter;

import com.alibaba.fastjson.JSON;
import com.joyadata.engine.common.beans.dto.DdlDTO;
import com.joyadata.engine.common.beans.dto.datasource.MetadataColumnDTO;
import com.joyadata.engine.common.beans.utils.dialect.AbstractDatabaseTypeConverter;
import com.joyadata.engine.common.beans.utils.dialect.paramters.DDLParameters;
import groovy.lang.Tuple2;
import groovy.lang.Tuple3;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.sql.Types;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2025/4/27 17:50
 */
@Slf4j
public class DmdbTypeConverter extends AbstractDatabaseTypeConverter {
    @Override
    protected Tuple3<String, String, String> doGenerateDDL(DDLParameters ddlParameters) {
        String schema = ddlParameters.getSchema();
        String tableName = ddlParameters.getTableName();
        List<DdlDTO> columns = ddlParameters.getColumns();
        String tableCnName = ddlParameters.getTableCnName();
        String fieldUppLower = ddlParameters.getFieldUppLower();
        if (StringUtils.isNotBlank(fieldUppLower)) {
            tableName = getUppOrLowerCase(fieldUppLower, tableName);
        }
        List<String> list = new ArrayList<>();
        String checkTableSql = "select 1 from " + tableName + " where 1=2";
        String create = "create table " + tableName + " (";
        if (StringUtils.isNotBlank(schema)) {
            if (StringUtils.isNotBlank(fieldUppLower)) {
                schema = getUppOrLowerCase(fieldUppLower, schema);
            }
            create = "create table " + schema + "." + tableName + " (";
            checkTableSql = "select 1 from " + schema + "." + tableName + " where 1=2";
        }
        StringBuilder ddl = new StringBuilder(create);
        String finalTableName = tableName;
        String finalSchema = schema;
        columns.forEach(ddlDTO -> {
            int dataType = ddlDTO.getDataTypeJava();// Integer.valueOf(column.get("data_type"));
            int columnSize = ddlDTO.getColumnSize();// Integer.valueOf(column.get("max_length"));
            int decimalDigits = ddlDTO.getDecimalDigits(); //Integer.valueOf(column.get("decimal_digits"));
            String dataName = ddlDTO.getDataType(); //column.get("type_name");
            String columnName;
            if (StringUtils.isNotBlank(fieldUppLower)) {
                columnName = getUppOrLowerCase(fieldUppLower, ddlDTO.getColumnName());
            } else {
                columnName = ddlDTO.getColumnName().toUpperCase();
            }
            String cnName = ddlDTO.getColumnCnName();// StringUtils.defaultString(column.get("remarks"),"");
            String dataTypeString = getDamengSQLTypeFromSqlType(dataType, dataName, columnSize, decimalDigits);
            ddl.append("\"").append(columnName).append("\" ").append(dataTypeString).append(",");
            if (StringUtils.isNotBlank(cnName)) {
                cnName = cnName.replaceAll("'", "`");
                cnName = cnName.replaceAll("\"", "“");
                if (StringUtils.isNotBlank(finalSchema)) {
                    list.add("COMMENT ON COLUMN \"" + finalSchema + "\".\"" + finalTableName + "\"." + columnName + " IS '" + cnName + "'");
                } else {
                    list.add("COMMENT ON COLUMN \"" + finalTableName + "\"." + columnName + " IS '" + cnName + "'");
                }
            }
            //comments.add();
        });
        //表中文名
        if (StringUtils.isNotBlank(tableCnName)) {
            tableCnName = tableCnName.replaceAll("'", "`");
            tableCnName = tableCnName.replaceAll("\"", "“");
            if (StringUtils.isNotBlank(schema)) {
                list.add("COMMENT ON TABLE  \"" + schema + "\".\"" + tableName + "\" IS '" + tableCnName + "'");
            } else {
                list.add("COMMENT ON TABLE  \"" + tableName + "\" IS '" + tableCnName + "'");
            }
        }
        String result = ddl.toString();
        result = result.substring(0, result.length() - 1);
        result = result + ")";
        log.info("getDamengDDLFromMetadata生成Dameng建表语句是 {}", result);
        log.info("getDamengDDLFromMetadata生成Dameng检测表是否存在的语句是 {}", checkTableSql);
        Tuple3<String, String, String> tuple3 = new Tuple3<>(result, JSON.toJSONString(list), checkTableSql);
        return tuple3;
    }

    private static String getDamengSQLTypeFromSqlType(int dataType, String dataName, int columnSize, int decimalDigits) {
        switch (dataType) {
            case Types.BIT:
                return "BIT";
            case Types.TINYINT:
            case Types.SMALLINT:
                return "SMALLINT";
            case Types.INTEGER:
                return "INTEGER";
            case Types.BIGINT:
                return "BIGINT";
            case Types.FLOAT:
                return " FLOAT";
            case Types.REAL:
                return "REAL";
            case Types.DOUBLE:
                return "DOUBLE PRECISION";
            case Types.NUMERIC:
                if (columnSize == 0 && decimalDigits == -127) {
                    return "NUMERIC";
                } else {
                    return "NUMERIC(" + columnSize + "," + decimalDigits + ")";
                }
            case Types.DECIMAL:
                if (columnSize == 0 || columnSize > 38) {
                    return "DECIMAL(38,0)";
                } else {
                    return "DECIMAL(" + columnSize + "," + decimalDigits + ")";
                }
            case Types.CHAR:
                return "CHAR(" + columnSize + ")";
            case Types.VARCHAR:
                if (columnSize > 4000 || columnSize == 0) {
                    return "CLOB";
                }
                return "VARCHAR(" + columnSize + ")";
            case Types.LONGVARCHAR:
                return "CLOB";
            case Types.DATE:
                if ("YEAR".equalsIgnoreCase(dataName)) {//mysql的year
                    return "SMALLINT";
                }
                return "DATE";
            case Types.TIME:
                return "TIME";
            case Types.TIMESTAMP:
                return "TIMESTAMP";
            case Types.BINARY:
                return "BINARY(" + columnSize + ")";
            case Types.VARBINARY:
                if (columnSize > 8000 || columnSize == 0) {
                    return "BLOB";
                }
                return "VARBINARY(" + columnSize + ")";
            case Types.LONGVARBINARY:
            case Types.BLOB:
                return "BLOB";
            case Types.CLOB:
                return "CLOB";
            case Types.NULL:
                return "NULL";
            default:
                if (StringUtils.isNotBlank(dataName)) {
                    if ("interval".equalsIgnoreCase(dataName) || "point".equalsIgnoreCase(dataName)
                            || "line".equalsIgnoreCase(dataName) || "lseg".equalsIgnoreCase(dataName)
                            || "box".equalsIgnoreCase(dataName) || "circle".equalsIgnoreCase(dataName)
                            || "inet".equalsIgnoreCase(dataName) || "cidr".equalsIgnoreCase(dataName)
                            || "json".equalsIgnoreCase(dataName) || "xml".equalsIgnoreCase(dataName)
                            || "uuid".equalsIgnoreCase(dataName) || "_int4".equalsIgnoreCase(dataName)
                            || "_text".equalsIgnoreCase(dataName) || "int4range".equalsIgnoreCase(dataName)
                            || "tsvector".equalsIgnoreCase(dataName) || "tsquery".equalsIgnoreCase(dataName)) {
                        return "CLOB";
                    } else if ("INTERVAL YEAR TO MONTH".equalsIgnoreCase(dataName)) {
                        return "INTERVAL YEAR TO MONTH";
                    } else if ("INTERVAL DAY TO SECOND".equalsIgnoreCase(dataName)) {
                        return "INTERVAL DAY TO SECOND";
                    } else if ("NCHAR".equalsIgnoreCase(dataName)) {
                        return "VARCHAR(" + columnSize + ")";
                    } else if ("NCLOB".equalsIgnoreCase(dataName) || "BINARY_FLOAT".equalsIgnoreCase(dataName) || "BINARY_DOUBLE".equalsIgnoreCase(dataName)) {
                        return "CLOB";
                    } else if ("INTERVAL YEAR(2) TO MONTH".equalsIgnoreCase(dataName)) {
                        return "VARCHAR(10)";
                    } else if ("BFILE".equalsIgnoreCase(dataName)) {
                        return "BLOB";
                    } else if (dataName.startsWith("TIMESTAMP")) {
                        return "TIMESTAMP";
                    } else if (dataName.equalsIgnoreCase("serial")) {//pg数据库转int
                        return "INTEGER";
                    }
                }

                return "VARCHAR(2000)";
        }
    }

    @Override
    public Tuple2<String, List<String>> generateAddPkSql(String schema, String tableName, List<MetadataColumnDTO> fieldList, String fieldUppLower) {
        List<String> pkFieldList = fieldList.stream().map(MetadataColumnDTO::getColumnName).collect(Collectors.toList());
        if (StringUtils.isNotBlank(fieldUppLower)) {
            tableName = getUppOrLowerCase(fieldUppLower, tableName);
            pkFieldList = convertListCase(pkFieldList, fieldUppLower);
        }
        String pkSql = "ALTER  TABLE  " + tableName + " ADD PRIMARY KEY (" + String.join(",", pkFieldList) + ")";
        if (StringUtils.isNotBlank(schema)) {
            if (StringUtils.isNotBlank(fieldUppLower)) {
                schema = getUppOrLowerCase(fieldUppLower, schema);
            }
            pkSql = "ALTER  TABLE " + schema + "." + tableName + " ADD PRIMARY KEY (" + String.join(",", pkFieldList) + ")";
        }
        Tuple2<String, List<String>> tuple2 = new Tuple2<>(pkSql, null);
        return tuple2;
    }
}
