package com.joyadata.engine.common.beans.dto.engine.source;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/3/14
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class EngineSourceMongoDBModel implements Serializable {
    /**
     * 如果未指定`result_table_name`，则该插件处理的数据不会注册为可被其他插件直接访问的数据集`(dataStream/dataset)`，
     * 也称为临时表`(table)`；如果指定了`result_table_name`，则该插件处理的数据会注册为可被其他插件直接访问的数据集`(dataStream/dataset)`，
     * 也称为临时表`(table)`。这里注册的数据集`(dataStream/dataset)`，其他插件通过指定`source_table_name`可直接进行访问。
     * https://seatunnel.apache.org/docs/2.3.3/connector-v2/source/common-options
     */
    private String resultTableName;
    /**
     * mongodb://192.168.0.100:27017/mydb、mongodb://192.168.0.1:27017,192.168.0.2:27017,192.168.0.3:27017/mydb?replicaSet=xxx
     */
    private String uri;
    /**
     * username
     */
    private String database;
    /**
     * 集合，对应表名
     */
    private String collection;

    /**
     * MongoDB's BSON and seatunnel data structure mapping
     * 数据结构：schema = {
     * fields {
     * c_map = "map<string, string>"
     * c_array = "array<int>"
     * c_string = string
     * c_boolean = boolean
     * c_int = int
     * c_bigint = bigint
     * c_double = double
     * c_bytes = bytes
     * c_date = date
     * c_decimal = "decimal(38, 18)"
     * c_timestamp = timestamp
     * c_row = {
     * c_map = "map<string, string>"
     * c_array = "array<int>"
     * c_string = string
     * c_boolean = boolean
     * c_int = int
     * c_bigint = bigint
     * c_double = double
     * c_bytes = bytes
     * c_date = date
     * c_decimal = "decimal(38, 18)"
     * c_timestamp = timestamp
     * }
     * }
     * }
     */
    private LinkedHashMap<String, String> schema;

    /**
     * In MongoDB, filters are used to filter documents for query operations. eg:"{status: \"A\"}"
     */
    private String query;

    /**
     * In MongoDB, Projection is used to control the fields contained in the query results.
     * 1m
     */
    private String projection;

    /**
     * 分区字段，默认为_id
     */
    private String partitionSplitKey;

    /**
     * 分区大小，默认为64 * 1024 * 1024
     */
    private Long partitionSplitSize;

    /**
     * MongoDB server normally times out idle cursors after an inactivity period (10 minutes) to prevent excess memory use. Set this option to true to prevent that. However, if the application takes longer than 30 minutes to process the current batch of documents, the session is marked as expired and closed，默认为true
     */
    private Boolean cursorNoTimeout;

    /**
     * 默认值为2048
     */
    private String fetchSize;

    /**
     * 默认值为600
     */
    private String maxTimeMin;

    /**
     * By utilizing flatSyncString, only one field attribute value can be set, and the field type must be a String. This operation will perform a string mapping on a single MongoDB data entry.默认为true
     */
    private Boolean flatSyncString;
}
