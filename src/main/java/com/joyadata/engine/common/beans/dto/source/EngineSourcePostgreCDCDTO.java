package com.joyadata.engine.common.beans.dto.source;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/3/2
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class EngineSourcePostgreCDCDTO implements Serializable {

    /**
     * 数据源id
     */
    private String datasourceInfoId;

    /**
     * 表id
     */
    private String tableId;

    /**
     * Debezium配置
     */
    private Map<String, String> debezium;


}
