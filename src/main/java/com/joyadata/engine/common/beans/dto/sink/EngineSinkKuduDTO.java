package com.joyadata.engine.common.beans.dto.sink;

import com.joyadata.engine.common.beans.dto.datasource.MetadataColumnDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.lang.reflect.Array;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/3/1
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class EngineSinkKuduDTO extends EngineBaseSinkDTO implements Serializable {

    /**
     * 写入表名称
     */
    private String tableName;
    /**
     * Kudu worker count. Default value is twice the current number of cpu cores. 默认值：2 * Runtime.getRuntime().availableProcessors()
     */
    private int clientWorkerCount;

    /**
     * Kudu normal operation time out. 默认值：30000
     */
    private Long clientDefaultOperationTimeoutMs;

    /**
     * Kudu admin operation time out. 默认值：30000
     */
    private Long clientDefaultAdminOperationTimeoutMs;

    /**
     * Kerberos principal enable. 默认值：false
     */
    private Boolean enableKerberos;

    /**
     * Kerberos principal. Note that all zeta nodes require have this file.
     */
    private String kerberosPrincipal;

    /**
     * Kerberos keytab. Note that all zeta nodes require have this file.
     */
    private Long kerberosKeytab;

    /**
     * Kerberos krb5 conf. Note that all zeta nodes require have this file.
     */
    private Boolean kerberosKrb5conf;

    /**
     * Storage mode, support `overwrite` and `append`.
     */
    private String saveMode;

    /**
     * Kudu flush mode. Default AUTO_FLUSH_SYNC.
     */
    private String sessionFlushMode;

    /**
     * The flush max size (includes all append, upsert and delete records), over this number of records, will flush data. The default value is 1024
     */
    private String batchSize;

    /**
     * The flush interval mills, over this time, asynchronous threads will flush data. 默认值：10000
     */
    private int bufferFlushInterval;

    /**
     * If true, ignore all not found rows. 默认值：false
     */
    private Boolean ignoreNotFound;

    /**
     * If true, ignore all dulicate rows. 默认值：false
     */
    private Boolean ignoreNotDuplicate;

    /**
     * 传输列
     */
    private List<MetadataColumnDTO> fieldList;

    /**
     * 任务是否建表
     */
    private Boolean createAlterTable;
}
