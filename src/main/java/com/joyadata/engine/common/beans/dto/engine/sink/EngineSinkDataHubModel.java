package com.joyadata.engine.common.beans.dto.engine.sink;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/7/2 16:53
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class EngineSinkDataHubModel implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     *  DataHub endpoint start with http
     */
    private String endpoint;
    /**
     *  DataHub accessId which cloud be access from Alibaba Cloud
     */
    private String accessId;

    /**
     *  DataHub accessKey which cloud be access from Alibaba Cloud
     */
    private String accessKey;

    /**
     *  DataHub project which is created in Alibaba Cloud
     */
    private String project;

    /**
     * DataHub topic
     */
    private String topic;

    /**
     * the max connection timeout 默认：3000
     */
    private Long timeout;

    /**
     * the max retry times when your client put record failed 默认：3
     */
    private Long retryTimes;

    /**
     * 当source_table_name未指定时，当前插件处理配置文件中前一个插件输出的dataset数据集。当指定source_table_name时，当前插件处理的是对应此参数的数据集。
     */
    private String sourceTableName;
}
