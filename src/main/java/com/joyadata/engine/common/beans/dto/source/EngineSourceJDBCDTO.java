package com.joyadata.engine.common.beans.dto.source;

import com.joyadata.engine.common.beans.dto.RowFilterDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/3/2
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class EngineSourceJDBCDTO extends EngineSourceDTO implements Serializable {

    /**
     * 数据源id
     */
    private String datasourceInfoId;

    /**
     * 表id
     */
    private String tableId;
    /**
     * 表名
     */
    private String tableName;

    /**
     * sql语句(sql查询的列 要和fieldList字段对应)
     */
    private String query;
    /**
     * where条件
     */
    private List<RowFilterDTO> whereCondition;

    /**
     * 用户自定义where
     */
    private String userDefineWhere;

    /**
     * 增量字段
     */
    private String incrField;
    /**
     * 并行读取拆分字段
     */
    private String partitionColumn;
    /**
     * 分片数量 设置增量字段必须设置分片数量
     */
    private String partitionNum;
    /**
     * 前置SQL
     */
    private List<String> preSql;
    /**
     * 后置SQL
     */
    private List<String> postSql;
    /**
     * 自定义SQL条件
     */
    private String customWhereSql;
    /**
     * jdbc连接超时时间, 单位毫秒
     */
    private Integer connectTimeoutMs;
    /**
     * 批量读取行数
     */
    private Integer batchSize;
    /**
     * 并发度
     */
    private Integer parallelism;

    /**
     * 数据库名字
     */
    private String dbName;
    /**
     * 模式名
     */
    private String schemaName;


    private String tablePath;

    /**
     * 空值处理
     */
    private String emptyDataStrategy;

    /**
     * 是否使用代理节点
     */
    private boolean useProxy;
    /**
     * tdsql代理的ip:port
     */
    private String ipPort;
    /**
     * 分区名称p0、p1...
     */
    private String proxyPartitionName;
}
