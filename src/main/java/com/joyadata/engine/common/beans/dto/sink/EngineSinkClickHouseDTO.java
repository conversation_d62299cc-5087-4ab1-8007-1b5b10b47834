package com.joyadata.engine.common.beans.dto.sink;

import com.joyadata.engine.common.beans.dto.datasource.MetadataColumnDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/3/1
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class EngineSinkClickHouseDTO implements Serializable {

    /**
     * 数据源id
     */
    private String datasourceInfoId;
    /**
     * Clickhouse表名
     */
    private String table;

    /**
     * Clickhouse表的主键
     */
    private String primaryKey;

    /**
     * 是否目标端做数据合并
     */
    private String merge = "false";
    /**
     * 传输列
     */
    private List<MetadataColumnDTO> fieldList;
    /**
     * 库名
     */
    private String dbName;
    /**
     * 模式名
     */
    private String schemaName;

    /**
     * 任务是否建表
     */
    private Boolean createAlterTable;

    /**
     * 前置SQL
     */
    private List<String> preSql;
    /**
     * 后置SQL
     */
    private List<String> postSql;
    /**
     * null，none、upper、lower
     */
    private String fieldUppLow;
}
