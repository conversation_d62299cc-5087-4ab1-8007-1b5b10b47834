package com.joyadata.engine.common.beans.dto.source;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/3/2
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class EngineSourceSqlserverCDCDTO implements Serializable {

    /**
     * 数据源id
     */
    private String datasourceInfoId;

    /**
     * 表id
     */
    private String tableId;

}
