package com.joyadata.engine.common.beans.utils.dialect.converter;

import com.alibaba.fastjson.JSON;
import com.joyadata.engine.common.beans.constants.Constants;
import com.joyadata.engine.common.beans.dto.DdlDTO;
import com.joyadata.engine.common.beans.dto.datasource.MetadataColumnDTO;
import com.joyadata.engine.common.beans.utils.dialect.AbstractDatabaseTypeConverter;
import com.joyadata.engine.common.beans.utils.dialect.paramters.DDLParameters;
import groovy.lang.Tuple2;
import groovy.lang.Tuple3;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.sql.Types;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2025/4/27 17:50
 */
@Slf4j
public class ClickhouseTypeConverter extends AbstractDatabaseTypeConverter {
    @Override
    protected Tuple3<String, String, String> doGenerateDDL(DDLParameters ddlParameters) {
        String schema = ddlParameters.getSchema();
        String tableName = ddlParameters.getTableName();
        List<DdlDTO> columns = ddlParameters.getColumns();
        String tableCnName = ddlParameters.getTableCnName();
        String fieldUppLower = ddlParameters.getFieldUppLower();
        if (StringUtils.isNotBlank(fieldUppLower)) {
            tableName = getUppOrLowerCase(fieldUppLower, tableName);
        }
        List<String> list = new ArrayList<>();
        String create = "create table " + tableName + " (";
        String checkTableSql = "select 1 from " + tableName + " where 1=2";
        if (StringUtils.isNotBlank(schema)) {
            if (StringUtils.isNotBlank(fieldUppLower)) {
                schema = getUppOrLowerCase(fieldUppLower, schema);
            }
            create = "create table " + schema + "." + tableName + " (";
            checkTableSql = "select 1 from " + schema + "." + tableName + " where 1=2";
        }
        StringBuilder ddl = new StringBuilder(create);
        String orderColumn;
        List<String> pkList = columns.stream().filter(c -> c.getPrimaryKey() == 1).map(DdlDTO::getColumnName).collect(Collectors.toList());
        boolean pkFlag = false;
        if (!pkList.isEmpty()) {
            orderColumn = String.join(",", pkList);
            //如果有主键，在这里设置一个标识，拼接sql时将创建主键的语句直接加上
            pkFlag = true;
        } else {
            //如果没有主键，就取时间类型字段排序，时间类型也没有就随便取个字段排序
            List<Integer> orderKeyList = Arrays.asList(Types.DATE, Types.TIME, Types.TIMESTAMP);
            Optional<String> first = columns.stream().filter(ddlDTO -> orderKeyList.contains(ddlDTO.getDataTypeJava())).map(DdlDTO::getColumnName).findFirst();
            if (first.isPresent()) {
                if (StringUtils.isNotBlank(fieldUppLower)) {
                    if (Constants.UPPER.equalsIgnoreCase(fieldUppLower)) {
                        orderColumn = first.get().toUpperCase();
                    } else if (Constants.LOWER.equalsIgnoreCase(fieldUppLower)) {
                        orderColumn = first.get().toLowerCase();
                    } else {
                        orderColumn = first.get();
                    }
                } else {
                    orderColumn = first.get().toLowerCase();
                }
            } else {
                if (StringUtils.isNotBlank(fieldUppLower)) {
                    if (Constants.UPPER.equalsIgnoreCase(fieldUppLower)) {
                        orderColumn = columns.get(0).getColumnName().toUpperCase();
                    } else if (Constants.LOWER.equalsIgnoreCase(fieldUppLower)) {
                        orderColumn = columns.get(0).getColumnName().toLowerCase();
                    } else {
                        orderColumn = columns.get(0).getColumnName();
                    }
                } else {
                    orderColumn = columns.get(0).getColumnName().toLowerCase();
                }
            }
        }
        for (DdlDTO currentColumn : columns) {
            int dataType = currentColumn.getDataTypeJava();// Integer.valueOf(column.get("data_type"));
            int columnSize = currentColumn.getColumnSize();// Integer.valueOf(column.get("max_length"));
            int decimalDigits = currentColumn.getDecimalDigits(); //Integer.valueOf(column.get("decimal_digits"));
            String dataName = currentColumn.getDataType(); //column.get("type_name");
            String columnName;
            if (StringUtils.isNotBlank(fieldUppLower)) {
                columnName = getUppOrLowerCase(fieldUppLower, currentColumn.getColumnName());
            } else {
                columnName = currentColumn.getColumnName().toLowerCase();
            }
            String cnName = currentColumn.getColumnCnName();// StringUtils.defaultString(column.get("remarks"),"");
            int primaryKey = currentColumn.getPrimaryKey();//主键字段不能加Nullable()
            //如果不是主键，但是是排序字段，也不能加Nullable()
            if (orderColumn.equals(columnName)) {
                primaryKey = 1;
            }
            String dataTypeString = getClickHouseTypeFromSqlType(dataType, dataName, columnSize, decimalDigits, primaryKey);
            if (StringUtils.isNotBlank(cnName)) {
                cnName = cnName.replaceAll("'", "`");
                cnName = cnName.replaceAll("\"", "“");
                ddl.append(columnName).append(" ").append(dataTypeString).append(" COMMENT '").append(cnName).append("',");
            } else {
                ddl.append(columnName).append(" ").append(dataTypeString).append(",");
            }
        }
        if (pkFlag) {
            ddl.append("PRIMARY KEY (").append(orderColumn).append("),");//这里加上逗号，下面会截取
        }
        String result = ddl.toString();
        result = result.substring(0, result.length() - 1);
        result = result + ") ENGINE = MergeTree() ORDER BY (" + orderColumn + ")";
        //表中文名
        if (StringUtils.isNotBlank(tableCnName)) {
            tableCnName = tableCnName.replaceAll("'", "`");
            tableCnName = tableCnName.replaceAll("\"", "“");
            result = result + " COMMENT '" + tableCnName + "'";
        }
        log.info("ClickHouseFromMetadata生成ClickHouse建表语句是 {}", result);
        log.info("ClickHouseFromMetadata生成ClickHouse检测表是否存在的语句是 {}", checkTableSql);
        Tuple3<String, String, String> tuple3 = new Tuple3<>(result, JSON.toJSONString(list), checkTableSql);
        return tuple3;
    }

    private static String getClickHouseTypeFromSqlType(int dataType, String dataName, int columnSize, int decimalDigits, int primaryKey) {
        if (primaryKey == 1) {//主键字段不能加Nullable()
            switch (dataType) {
                case Types.BIT:
                case Types.BOOLEAN:
                case Types.TINYINT:
                case Types.SMALLINT:
                    return "Int8";
                case Types.INTEGER:
                    return "Int32";
                case Types.BIGINT:
                    return "Int64";
                case Types.DOUBLE:
                case Types.FLOAT:
                    return "Float64";
                case Types.REAL:
                    return "Float32";
                case Types.NUMERIC:
                case Types.DECIMAL:
                    if (decimalDigits == 0) {
                        if (columnSize < 10) {
                            return "Int32";
                        } else if (columnSize < 19) {
                            return "Int64";
                        } else if (columnSize > 65) {
                            return "Decimal( 65 )";
                        } else {
                            return "Decimal(" + columnSize + ")";
                        }
                    } else if (decimalDigits == -127) {
                        if (columnSize == 0 || columnSize > 65) {
                            return "Decimal(" + 65 + ", " + 0 + ")";
                        } else {
                            return "Decimal(" + columnSize + ", " + 0 + ")";
                        }
                    } else {
                        if ("float4".equalsIgnoreCase(dataName) || "float8".equalsIgnoreCase(dataName)) {//pg字段处理
                            return "Float64";
                        }
                        return "Decimal(" + columnSize + ", " + decimalDigits + ")";
                    }
                case Types.DATE:
                    return "Date";
                case Types.TIME:
                case Types.TIMESTAMP:
                    return "DateTime";
                case Types.BINARY:
                case Types.VARBINARY:
                case Types.LONGVARBINARY:
                case Types.CHAR:
                case Types.VARCHAR:
                case Types.NCHAR:
                case Types.NVARCHAR:
                case Types.LONGNVARCHAR:
                case Types.LONGVARCHAR:
                case Types.CLOB:
                case Types.NCLOB:
                case Types.BLOB:
                    return "String";
                default:
                    if (StringUtils.isNotBlank(dataName)) {
                        if ("NVARCHAR2".equalsIgnoreCase(dataName)) {
                            return "String";
                        } else if ("VARCHAR2".equalsIgnoreCase(dataName)) {
                            return "String";
                        } else if (dataName.startsWith("TIMESTAMP")) {
                            return "DateTime";
                        } else if ("INTERVAL YEAR(2) TO MONTH".equalsIgnoreCase(dataName)) {
                            return "String";
                        } else if (dataName.equalsIgnoreCase("serial")) {//pg数据库转int
                            return "Int32";
                        }
                    }
                    return "String";
            }
        } else {//非主键字段加Nullable()，允许为空
            switch (dataType) {
                case Types.BIT:
                case Types.BOOLEAN:
                case Types.TINYINT:
                case Types.SMALLINT:
                    return "Nullable(Int8)";
                case Types.INTEGER:
                    return "Nullable(Int32)";
                case Types.BIGINT:
                    return "Nullable(Int64)";
                case Types.DOUBLE:
                case Types.FLOAT:
                    return "Nullable(Float64)";
                case Types.REAL:
                    return "Nullable(Float32)";
                case Types.NUMERIC:
                case Types.DECIMAL:
                    if (decimalDigits == 0) {
                        if (columnSize < 10) {
                            return "Nullable(Int32)";
                        } else if (columnSize < 19) {
                            return "Nullable(Int64)";
                        } else if (columnSize > 65) {
                            return "Nullable(Decimal( 65 ))";
                        } else {
                            return "Nullable(Decimal(" + columnSize + "))";
                        }
                    } else if (decimalDigits == -127) {
                        if (columnSize == 0 || columnSize > 65) {
                            return "Nullable(Decimal(" + 65 + ", " + 0 + "))";
                        } else {
                            return "Nullable(Decimal(" + columnSize + ", " + 0 + "))";
                        }
                    } else {
                        if ("float4".equalsIgnoreCase(dataName) || "float8".equalsIgnoreCase(dataName)) {//pg字段处理
                            return "Nullable(Float64)";
                        }
                        return "Nullable(Decimal(" + columnSize + ", " + decimalDigits + "))";
                    }
                case Types.DATE:
                    return "Nullable(Date)";
                case Types.TIME:
                case Types.TIMESTAMP:
                    return "Nullable(DateTime)";
                case Types.BINARY:
                case Types.VARBINARY:
                case Types.LONGVARBINARY:
                case Types.CHAR:
                case Types.VARCHAR:
                case Types.NCHAR:
                case Types.NVARCHAR:
                case Types.LONGNVARCHAR:
                case Types.LONGVARCHAR:
                case Types.CLOB:
                case Types.NCLOB:
                case Types.BLOB:
                    return "Nullable(String)";
                default:
                    if (StringUtils.isNotBlank(dataName)) {
                        if ("NVARCHAR2".equalsIgnoreCase(dataName)) {
                            return "Nullable(String)";
                        } else if ("VARCHAR2".equalsIgnoreCase(dataName)) {
                            return "Nullable(String)";
                        } else if (dataName.startsWith("TIMESTAMP")) {
                            return "Nullable(DateTime)";
                        } else if ("INTERVAL YEAR(2) TO MONTH".equalsIgnoreCase(dataName)) {
                            return "Nullable(String)";
                        } else if (dataName.equalsIgnoreCase("serial")) {//pg数据库转int
                            return "Nullable(Int32)";
                        }
                    }
                    return "Nullable(String)";
            }
        }
    }

    @Override
    public Tuple2<String, List<String>> generateAddPkSql(String schema, String tableName, List<MetadataColumnDTO> pkFieldList, String fieldUppLower) {
        return new Tuple2<>("", null);
    }
}
