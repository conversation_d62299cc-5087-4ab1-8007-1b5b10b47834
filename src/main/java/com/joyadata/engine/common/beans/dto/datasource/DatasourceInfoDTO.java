package com.joyadata.engine.common.beans.dto.datasource;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version V1.0
 * @ClassName: DatasourceInfoDTO
 * @date 2024/2/22
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class DatasourceInfoDTO {
    /**
     * 数据源信息业务主键
     */
    private String datasourceInfoId;
    /**
     * 数据源类型唯一 如Mysql, Oracle, Hive
     */
    private String dataType;
    /**
     * 数据源连接信息
     */
    private String dataJson;
    /**
     * 数据库名称
     */
    private String dbName;
    /**
     * 数据库版本
     */
    private String dataVersion;

    public String getDatasourceInfoId() {
        return datasourceInfoId;
    }

    public void setDatasourceInfoId(String datasourceInfoId) {
        this.datasourceInfoId = datasourceInfoId;
    }

    public String getDataType() {
        return dataType;
    }

    public void setDataType(String dataType) {
        this.dataType = dataType;
    }

    public String getDataJson() {
        return dataJson;
    }

    public void setDataJson(String dataJson) {
        this.dataJson = dataJson;
    }

    public String getDbName() {
        return dbName;
    }

    public void setDbName(String dbName) {
        this.dbName = dbName;
    }
}
