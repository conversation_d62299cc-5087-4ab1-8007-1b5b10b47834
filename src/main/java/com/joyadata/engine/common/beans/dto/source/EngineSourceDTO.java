package com.joyadata.engine.common.beans.dto.source;


import com.joyadata.engine.common.beans.enums.EngineSourceTypeEnum;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version V1.0
 * @ClassName: EngineSourceDTO
 * @date 2024/2/21
 */
@Data
public class EngineSourceDTO implements Serializable {
    /**
     * 类型
     */
    private EngineSourceTypeEnum type;

    /**
     * 组件名称
     */
    private String configName;

    /**
     * 如果未指定`result_table_name`，则该插件处理的数据不会注册为可被其他插件直接访问的数据集`(dataStream/dataset)`，
     * 也称为临时表`(table)`；如果指定了`result_table_name`，则该插件处理的数据会注册为可被其他插件直接访问的数据集`(dataStream/dataset)`，
     * 也称为临时表`(table)`。这里注册的数据集`(dataStream/dataset)`，其他插件通过指定`source_table_name`可直接进行访问。
     */
    private String resultTableName;

    private EngineSourceElasticsearchDTO elasticsearchSource;

    private EngineSourceJDBCDTO jdbcSource;

    private EngineSourceMongoDBDTO mongodbSource;

    private EngineSourceKuduDTO kuduSource;

    private EngineSourceMysqlCDCDTO mysqlCdcSource;

    private EngineSourceOracleCDCDTO oracleCdcSource;

    private EngineSourcePostgreCDCDTO postgresqlCdcSource;

    private EngineSourceSqlserverCDCDTO sqlserverCdcSource;

    private EngineSourceHdfsFileDTO hdfsFileSource;

    private EngineSourceLocalFileDTO localFileSource;

    private EngineSourceSFTPDTO sftpFileSource;

    private EngineSourceFTPDTO ftpFileSource;

    private EngineSourceOssAliFileDTO ossAliFileSource;

    private EngineSourceKafkaDTO kafkaSource;

    private EngineSourceS3FileDTO s3FileSource;

    private EngineSourceDorisDTO dorisSource;

    private EngineSourceOssHuaweiFileDTO ossHuaweiFileSource;

    private EngineSourceDataHubDTO dataHubSource;

    /**
     * adb gpdist 源端
     */
    private EngineSourceAdbGpdistDTO adbGpdistSource;

    private EngineSourceClickhouseDTO clickhouseSource;
    private EngineSourceHbaseDTO hbaseSource;

    private EngineSourceLocalFile2FileDTO localFile2FileSource;
    private EngineSourceOssAliFile2FileDTO ossAliFile2FileSource;
    private EngineSourceS3File2FileDTO s3File2FileSource;
    private EngineSourceSFTP2FileDTO sftp2FileSource;
    private EngineSourceInspurOSSFile2FileDTO inspurOSSFile2FileSource;
    /**
     * argo hdfs 源端
     */
    private EngineSourceArgoHdfsFileDTO argoHdfsFileSource;


    private EngineHttpBaseSourceDTO httpBaseSource;
    private EngineMaxComputeSourceDTO maxComputeSource;

    //任务同步进度统计是否开启
    private Boolean syncProgress;
}
