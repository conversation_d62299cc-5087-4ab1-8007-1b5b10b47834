package com.joyadata.engine.common.beans.constants;

/**
 * <AUTHOR>
 * @date 2024/2/26
 */
public class Constants {
    /**
     * string
     * dedp:database:datasource:{tenant_code}:{数据源id}
     */
    public static final String ENGINE_DATA_SOURCE_KEY = "dedp:database:datasource:";

    /**
     * hash
     * dedp:database:table:{tenant_code}:{表id}
     */
    public static final String ENGINE_METADATA_KEY = "dedp:database:table:";


    /**
     * 临时存储，有过期时间
     */
    public static final String ENGINE_NEED_SAVEPOINT = "dedp:database:engine:needsavepoint:";

    /**
     * 位点存储
     */
    public static final String ENGINE_SAVEPOINT = "dedp:database:engine:savepoint:";

    public static final String UPPER = "upper";
    public static final String LOWER = "lower";
    public static final String NONE = "none";
}
