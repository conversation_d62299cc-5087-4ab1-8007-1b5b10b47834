package com.joyadata.engine.common.beans.dto.engine.sink;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/7/9 16:04
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class EngineSinkArgoHdfsFileModel implements Serializable {
    private static final long serialVersionUID = 1L;

    private String defaultFS;

    private String path;

    /**
     * 文件类型
     */
    private String fileFormatType;
    /**
     * 数据分隔符
     * Only used when file_format_type is text
     */
    private String fieldDelimiter;
    /**
     * 每行的数据分隔符
     * Only used when file_format_type is text
     */
    private String rowDelimiter;

    private String fileNameExpression;

    private String argoUrl;
    private String argoUser;
    private String argoPassword;
    private String argoSchema;
    private String argoTable;
    private String argoTmpTableName;
    private String krb5Path;
    private String kerberosPrincipal;
    private String kerberosKeytabPath;
    private String hdfsSitePath;


    /**
     * 当source_table_name未指定时，当前插件处理配置文件中前一个插件输出的dataset数据集。当指定source_table_name时，当前插件处理的是对应此参数的数据集。
     */
    private String sourceTableName;
    /**
     * 空值处理
     */
    private String emptyDataStrategy;
    /**
     * 外表schema
     */
    private String argoTmpSchema;
}