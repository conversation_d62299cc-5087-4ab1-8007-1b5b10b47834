package com.joyadata.engine.common.beans.utils;

import org.jasypt.encryption.pbe.StandardPBEStringEncryptor;
import org.jasypt.iv.RandomIvGenerator;
import org.jasypt.salt.RandomSaltGenerator;

public class JasyptDecryptor {
    public static void main(String[] args) {
        String algorithm = "PBEWITHHMACSHA512ANDAES_256";
        String password = "NkVCQUQxMjBFQTI4QjY5NzVFQkYxRUNBRjEzMjc1Nzc=";
        mydecrypt(password, algorithm);
        myencrypt(password, algorithm);
    }

    private static void myencrypt(String password, String algorithm) {
        String plainText = "zhangsan"; // 替换为你的明文

        // 创建加密器（配置需与解密一致）
        StandardPBEStringEncryptor encryptor = new StandardPBEStringEncryptor();
        encryptor.setPassword(password);
        encryptor.setAlgorithm(algorithm); // 算法与解密一致
        encryptor.setSaltGenerator(new RandomSaltGenerator());
        encryptor.setIvGenerator(new RandomIvGenerator());

        try {
            // 执行加密
            String encryptedText = encryptor.encrypt(plainText);
            System.out.println("加密结果: " + encryptedText);
        } catch (Exception e) {
            System.err.println("加密失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    private static void mydecrypt(String password, String algorithm) {
        // 配置参数
        //String encryptedInput = "cMU+KY4HfzCJHRKCopKBFXppHTBtBDjNwC+bcOAlURV0Tqt2jLzzjrS4r8dok78w";
        String encryptedInput = "jFW155h49N4tBpw4JUDcHcXm+mubXkH2X3l1yDPt+CCPUXw4O9vP6C/RGYfBJCeG";
        // 创建解密器
        StandardPBEStringEncryptor encryptor = new StandardPBEStringEncryptor();
        encryptor.setPassword(password);
        //加密算法
        encryptor.setAlgorithm(algorithm);
        encryptor.setSaltGenerator(new RandomSaltGenerator());
        encryptor.setIvGenerator(new RandomIvGenerator());

        try {
            // 执行解密
            String decrypted = encryptor.decrypt(encryptedInput);
            System.out.println("解密结果: " + decrypted);
        } catch (Exception e) {
            System.err.println("解密失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}

