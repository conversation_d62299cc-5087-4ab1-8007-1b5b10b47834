package com.joyadata.engine.common.beans.dto.engine.sink;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/3/18
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class EngineSinkHiveModel extends EngineBaseSinkModel implements Serializable {
    /**
     * Hive metastore uri
     */
    private String metastoreUri ;
    /**
     * 写入表名称
     */
    private String tableName;

    /**
     * hdfs-site.xml的路径，用于加载名称节点的ha配置
     */
    private String hdfsSitePath;

    /**
     * hive-site.xml的路径，用于验证配置单元元存储
     */
    private String hiveSitePath ;

    /**
     * 是否开启Kerberos认证
     */
    private String useKerberos ;

    /**
     * kerberos的krb5路径
     */
    private String krb5Path ;

    /**
     * The principal of kerberos
     */
    private String kerberosPrincipal ;

    /**
     * kerberos的keytab路径
     */
    private String kerberosKeytabPath ;
    /**
     * YYYY_MM_DD_HH_MM_SS("yyyy-MM-dd HH:mm:ss"),
     * YYYY_MM_DD_HH_MM_SS_SSSSSS("yyyy-MM-dd HH:mm:ss.SSSSSS"),
     * YYYY_MM_DD_HH_MM_SS_SPOT("yyyy.MM.dd HH:mm:ss"),
     * YYYY_MM_DD_HH_MM_SS_SLASH("yyyy/MM/dd HH:mm:ss"),
     * YYYY_MM_DD_HH_MM_SS_NO_SPLIT("yyyyMMddHHmmss"),
     * YYYY_MM_DD_HH_MM_SS_ISO8601("yyyy-MM-dd'T'HH:mm:ss"),
     * YYYY_MM_DD_HH_MM_SS_SSS_ISO8601("yyyy-MM-dd'T'HH:mm:ss.SSS"),
     * YYYY_MM_DD_HH_MM_SS_SSSSSS_ISO8601("yyyy-MM-dd'T'HH:mm:ss.SSSSSS"),
     * YYYY_MM_DD_HH_MM_SS_SSSSSSSSS_ISO8601("yyyy-MM-dd'T'HH:mm:ss.SSSSSSSSS");
     */
    private String datetimeFormat = "yyyy-MM-dd HH:mm:ss";

    /**
     * YYYY_MM_DD("yyyy-MM-dd"),
     * YY_MM_DD("yy-MM-dd"),
     * YYYY_MM_DD_SPOT("yyyy.MM.dd"),
     * YYYY_MM_DD_SLASH("yyyy/MM/dd");
     */
    private String dateFormat = "yyyy-MM-dd";
    /**
     * HH_MM_SS("HH:mm:ss"),
     * HH_MM_SS_SSS("HH:mm:ss.SSS");
     */
    private String timeFormat = "HH:mm:ss";
    /**
     * 远程用户名称
     */
    private String remoteUser;
}
