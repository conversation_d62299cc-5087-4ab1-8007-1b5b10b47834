package com.joyadata.engine.common.beans.dto.sink;

import com.joyadata.engine.common.beans.dto.datasource.MetadataColumnDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/7/2 16:53
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class EngineSinkDataHubDTO extends EngineBaseSinkDTO implements Serializable {

    /**
     *  DataHub endpoint start with http
     */
    private String endpoint;
    /**
     *  DataHub accessId which cloud be access from Alibaba Cloud
     */
    private String accessId;

    /**
     *  DataHub accessKey which cloud be access from Alibaba Cloud
     */
    private String accessKey;

    /**
     *  DataHub project which is created in Alibaba Cloud
     */
    private String project;

    /**
     * DataHub topic
     */
    private String topic;

    /**
     * the max connection timeout 默认：3000
     */
    private Long timeout;

    /**
     * the max retry times when your client put record failed 默认：3
     */
    private Long retryTimes;

    /**
     * 传输列
     */
    private List<MetadataColumnDTO> fieldList;
}
