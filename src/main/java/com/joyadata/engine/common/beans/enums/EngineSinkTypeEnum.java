package com.joyadata.engine.common.beans.enums;

/**
 * <AUTHOR>
 * @version V1.0
 * @ClassName: EngineSinkType
 * @date 2024/2/21
 */
public enum EngineSinkTypeEnum {
    /**
     * sink
     */
    JDBC,
    CLICKHOUSE,
    KAFKA,
    HIVE,
    INCEPTOR,
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    SFTP<PERSON><PERSON>,
    FTP<PERSON><PERSON>,
    OSS_ALI,
    S3<PERSON>LE,
    DORIS,
    CONSOLE_HOLE,
    OSS_HUAWEI,
    DATA_HUB,
    ARGO_HDFS_FILE,
    <PERSON>LA<PERSON>ICSEARCH,
    HBASE,
    LOCAL<PERSON><PERSON><PERSON><PERSON><PERSON>,
    OSS_ALI2<PERSON>LE,
    S3<PERSON><PERSON>2<PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    INSPUROSSF<PERSON>E2<PERSON><PERSON>,
    <PERSON><PERSON>_GPDIST,
    DWS_PG,
    HTTP,
    MAXCOMPUTE
}
