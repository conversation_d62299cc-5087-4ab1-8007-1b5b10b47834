package com.joyadata.engine.common.beans.dto;


import com.joyadata.engine.common.beans.enums.DatabaseTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/2/20
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class DatabaseConnectionDTO {
    private String url;
    private String userName;
    private String password;
    private DatabaseTypeEnum databaseType;
    private String schemaName;
    private String accessKey;
    private String accessSecret;
    private String endpoint;
    private String host;
    private String port;
    private String bucket;
    private String beNodes;
    private String feNodes;
    private String accessId;//dataHub
    private String project;//dataHub
    private String topic;//dataHub
    private String defaultFS;//argo HDFS 配置
    private String driverClassName;
    private String datasourceMetadata;//Hive元数据库数据源id
}
