package com.joyadata.engine.common.beans.dto.engine.sink;


import lombok.Data;

import java.io.Serializable;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/3/18
 */
@Data
public class EngineBaseSinkModel implements Serializable {

    /**
     * 当source_table_name未指定时，当前插件处理配置文件中前一个插件输出的dataset数据集。当指定source_table_name时，当前插件处理的是对应此参数的数据集。
     */
    private String sourceTableName;

    /**
     * 并发度
     * 当未指定`parallelism`时，默认使用`env`中的`parallelism`值；当指定`parallelism`时，将覆盖`env`中的`parallelism`值
     */
    private String parallelism;

}
