package com.joyadata.engine.common.beans.dto.sink;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/3/18
 */
@Data
public class EngineBaseSinkDTO implements Serializable {

    /**
     * 数据源ID
     */
    private String datasourceInfoId;


    /**
     * 并发度
     * 当未指定`parallelism`时，默认使用`env`中的`parallelism`值；当指定`parallelism`时，将覆盖`env`中的`parallelism`值
     */
    private String parallelism;
}
