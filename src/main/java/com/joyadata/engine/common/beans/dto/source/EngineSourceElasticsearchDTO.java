package com.joyadata.engine.common.beans.dto.source;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.LinkedHashMap;
import java.util.List;

/**
 * @desc 添加es的源信息框架
 * <AUTHOR>
 * @date 2024/3/14
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class EngineSourceElasticsearchDTO extends EngineBaseSourceDTO implements Serializable {

    /**
     * 表id
     */
    private String tableId;

    /**
     * index
     */
    private String index;

    /**
     * 索引的字段。您可以通过指定字段_id来获取文档id。如果将_id汇到其他索引，则由于Elasticsearch的限制，您需要为_id指定一个别名。如果不配置源，则必须配置架构。
     * source	array	no
     */
    private List<String> source;

    /**
     * Elasticsearch DSL。您可以控制读取数据的范围。
     * json {"match_all": {}}
     */
    private String query ;
    /**
     * Elasticsearch为滚动请求保持搜索上下文活动的时间量。
     * 1m
     */
    private String scrollTime;

    /**
     * 每个Elasticsearch滚动请求返回的最大点击数。
     * 100
     */
    private int scrollSize;

    /**
     * 数据的结构，包括字段名称和字段类型。如果不配置schema，则必须配置source。
     *
     * schema = {
     *         fields {
     *             c_map = "map<string, tinyint>"
     *             c_array = "array<tinyint>"
     *             c_string = string
     *             c_boolean = boolean
     *             c_tinyint = tinyint
     *             c_smallint = smallint
     *             c_int = int
     *             c_bigint = bigint
     *             c_float = float
     *             c_double = double
     *             c_decimal = "decimal(2, 1)"
     *             c_bytes = bytes
     *             c_date = date
     *             c_timestamp = timestamp
     *         }
     *      }
     */
    private LinkedHashMap<String,String> schema;

    //剩余还有一些字段不知道什么含义，未测试，暂时不添加


}
