package com.joyadata.engine.common.beans.dto.sink;

import com.joyadata.engine.common.beans.dto.datasource.MetadataColumnDTO;
import com.joyadata.engine.common.beans.enums.HiveStorageTypeEnum;
import com.joyadata.engine.common.beans.enums.SinkPreDdlEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/3/18
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class EngineSinkHiveDTO extends EngineBaseSinkDTO implements Serializable {

    /**
     * 写入表名称
     */
    private String tableName;

    /**
     * Hive metastore uri
     */
    private String metastoreUri;

    /**
     * hdfs-site.xml的路径，用于加载名称节点的ha配置
     */
    private String hdfsSitePath;

    /**
     * hive-site.xml的路径，用于验证配置单元元存储
     */
    private String hiveSitePath;

    /**
     * kerberos的krb5路径
     */
    private String krb5Path;

    /**
     * The principal of kerberos
     */
    private String kerberosPrincipal;

    /**
     * kerberos的keytab路径
     */
    private String kerberosKeytabPath;
    /**
     * 传输列
     */
    private List<MetadataColumnDTO> fieldList;

    /**
     * 任务是否建表
     */
    private Boolean createAlterTable;

    /**
     * hive存储类型
     */
    private HiveStorageTypeEnum hiveStorageType;

    /**
     * null，none、upper、lower
     */
    private String fieldUppLow;

    /**
     * NONE 不做处理
     * DROP 修改表名称为:DROP_YYYMMDDHHMMSS_原表名称
     * TRUNACATE 修改表名称为:TRUNACATE_YYYMMDDHHMMSS_原表名称
     */
    private SinkPreDdlEnum sinkPreDdl;
}
