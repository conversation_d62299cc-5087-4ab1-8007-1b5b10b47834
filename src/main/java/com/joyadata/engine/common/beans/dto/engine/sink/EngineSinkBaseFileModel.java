//package com.joyadata.engine.common.beans.dto.engine.sink;
//
//
//import lombok.Data;
//
//import java.io.Serializable;
//
///**
// * <AUTHOR>
// * @date 2024/3/18
// */
//@Data
//public class EngineSinkBaseFileModel extends EngineBaseSinkModel implements Serializable {
//    /**
//     * 数据源路径
//     */
//    private String path;
//
//    /**
//     * 临时路径
//     */
//    private String tmpPath;
//
//    /**
//     * 是否自定义文件名
//     */
//    private boolean customFilename;
//
//    /**
//     * 文件名表达式
//     */
//    private String fileNameExpression;
//
//    /**
//     * 文件名时间格式
//     */
//    private String filenameTimeFormat;
//
//    /**
//     * 文件格式类型
//     */
//    private String fileFormatType;
//
//    /**
//     * 字段分隔符
//     */
//    private String fieldDelimiter;
//
//    /**
//     * 行分隔符
//     */
//    private String rowDelimiter;
//
//    /**
//     * 是否有分区
//     */
//    private boolean havePartition;
//
//    /**
//     * 分区方式
//     */
//    private String[] partitionBy;
//
//    /**
//     * 分区目录表达式
//     */
//    private String partitionDirExpression;
//
//    /**
//     * 是否将分区字段写入文件
//     */
//    private boolean isPartitionFieldWriteInFile;
//
//    /**
//     * 输出列
//     */
//    private String[] sinkColumns;
//
//    /**
//     * 是否启用事务
//     */
//    private boolean isEnableTransaction;
//
//    /**
//     * 批处理大小
//     */
//    private int batchSize;
//
//    /**
//     * 压缩编解码器
//     */
//    private String compressCodec;
//
//    /**
//     * 最大内存行数
//     */
//    private int maxRowsInMemory;
//
//    /**
//     * 表格名称
//     */
//    private String sheetName;
//
//    /**
//     * 是否写入表头
//     */
//    private boolean enableHeaderWrite;
//
//
//}
