package com.joyadata.engine.common.beans.dto.engine.source;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/2/21
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class EngineSourceKafkaModel implements Serializable {
    /**
     * 如果未指定`result_table_name`，则该插件处理的数据不会注册为可被其他插件直接访问的数据集`(dataStream/dataset)`，
     * 也称为临时表`(table)`；如果指定了`result_table_name`，则该插件处理的数据会注册为可被其他插件直接访问的数据集`(dataStream/dataset)`，
     * 也称为临时表`(table)`。这里注册的数据集`(dataStream/dataset)`，其他插件通过指定`source_table_name`可直接进行访问。
     * https://seatunnel.apache.org/docs/2.3.3/connector-v2/source/common-options
     */
    private String resultTableName;
    /**
     * kafka主题
     */
    private String topic;
    /**
     * 服务器地址。如"192.168.90.104:9092"
     */
    private String bootstrapServers;
    /**
     * 如果pattern设置为true，则要从中读取的主题名称模式的正则表达式。客户端中名称与指定正则表达式匹配的所有主题都将由消费者订阅。
     */
    private String pattern;
    /**
     * 消费组
     */
    private String consumerGroup;
    /**
     * 如果为true，则消费者的偏移量将在后台定期提交。
     */
    private String commitOnCheckpoint;
    /**
     * kafka配置，如：
     * {
     * acks = "all"
     * request.timeout.ms = 60000
     * buffer.memory = 33554432
     * }
     */
    private Map<String, String> kafkaConfig;
    /**
     * 上游数据的模式字段
     */
    private LinkedHashMap<String, String> schema;
    
    private LinkedHashMap<String, String> mappers;
    /**
     * 数据格式
     */
    private String format;
    /**
     * 数据格式错误的处理方法。默认值为fail，可选值为(fail, skip)。当选择fail时，数据格式错误将阻塞并抛出异常。当选择跳过时，数据格式错误将跳过此行数据。
     */
    private String formatErrorHandleWay;
    /**
     * 字段分隔符
     */
    private String fieldDelimiter;
    /**
     * 消费模式：[earliest],[group_offsets],[latest],[specific_offsets],[timestamp]
     */
    private String startMode;

    /**
     * 消费模式所需的偏移量为specific_offsets.
     */
    private String startModeOffsets;
    /**
     * 消费模式所需的偏移量为timestamp.
     */
    private String startModeTimestamp;
    /**
     * 动态发现主题和分区的时间间隔.
     */
    private String partitionDiscoveryIntervalMillis;
}
