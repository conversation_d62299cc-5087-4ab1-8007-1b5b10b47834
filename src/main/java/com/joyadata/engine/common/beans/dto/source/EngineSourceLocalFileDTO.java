package com.joyadata.engine.common.beans.dto.source;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.LinkedHashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @ClassName: EngineSourceLocalFileDTO
 * @date 2024/5/28
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class EngineSourceLocalFileDTO implements Serializable {
    /**
     * 源文件路径
     */
    private String path;
    /**
     * 支持文件类型 text、csv、parquet、orc、json、excel
     */
    private String fileFormatType;
    /**
     * 读取数据源的列表，用户可以使用它来实现字段投影
     */
    private List<String> readColumns;
    /**
     * file_format_type 为text的时候需要配置
     */
    private String fieldDelimiter;
    /**
     * 控制是否从文件路径解析分区键和值
     */
    private Boolean parsePartitionFromPath;
    /**
     * 日期类型格式 yyyy-MM-dd yyyy.MM.dd yyyy/MM/dd，默认yyyy-MM-dd
     */
    private String dateFormat = "yyyy-MM-dd";
    /**
     * Datetime类型格式， yyyy-MM-dd HH:mm:ss yyyy.MM.dd HH:mm:ss yyyy/MM/dd HH:mm:ss yyyyMMddHHmmss
     * 默认yyyy-MM-dd HH:mm:ss
     */
    private String datetimeFormat = "yyyy-MM-dd HH:mm:ss";
    /**
     * 时间类型格式
     */
    private String timeFormat = "HH:mm:ss";
    /**
     * 跳过前几行，但只针对 txt 和 csv
     */
    private long skipHeaderRowNumber;

    private LinkedHashMap<String, String> schema;
    /**
     * 只有当file_format为excel时才需要
     */
    private String sheetName;
    /**
     * 过滤模式，用于过滤文件
     */
    private String fileFilterPattern;
    /**
     * TXT：lzo none
     * json：lzo none
     * 文件名:lzo none
     * orc/parquet：
     * 自动识别压缩类型，无需额外设置
     */
    private String compressCodec;

    private String encoding;
}
