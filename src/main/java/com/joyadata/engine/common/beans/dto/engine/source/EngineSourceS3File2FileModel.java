package com.joyadata.engine.common.beans.dto.engine.source;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class EngineSourceS3File2FileModel {
    private String path;

    private String bucket;

    private String accessKey;

    private String secretKey;

    private String fsS3aEndpoint;

    private String fsS3aAwsCredentialsProvider = "org.apache.hadoop.fs.s3a.SimpleAWSCredentialsProvider";

    private String fileFormatType = "binary";
    /**
     * 结果表名称
     */
    private String resultTableName;
}
