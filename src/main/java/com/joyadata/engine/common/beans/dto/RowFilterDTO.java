package com.joyadata.engine.common.beans.dto;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version V1.0
 * @ClassName: RowFilterDTO
 * @date 2024/2/21
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class RowFilterDTO {
    /**
     * 条件值
     */
    private String value;
    /**
     * 数据类型(java.sql.Types)
     */
    private Object dataType;//兼容旧任务，使用object类型，海豚中如果转int失败，就给java.sql.Types的字符串类型
    /**
     * 条件
     */
    private String condition;
    /**
     * 字段名
     */
    private String fieldName;

}
