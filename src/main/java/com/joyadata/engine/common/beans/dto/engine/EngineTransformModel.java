package com.joyadata.engine.common.beans.dto.engine;


import com.joyadata.engine.common.beans.enums.EngineTransformTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/2/21
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class EngineTransformModel implements Serializable {

    /**
     * 目前支持的转换类型
     * Copy("Copy"),
     * FieldMapper("FieldMapper"),
     * FilterRowKind("FilterRowKind"),
     * Filter("Filter"),
     * Replace("Replace"),
     * Split("Split"),
     * SQL("SQL");
     */
    private EngineTransformTypeEnum engineTransformType;

    /**
     * 源端配置的 resultTableName
     */
    private String sourceTableName;
    /**
     * 当前transfor返回的名称，用于目标端使用
     */
    private String resultTableName;

    /**
     * fields {
     * name1 = name
     * name2 = name
     * age1 = age
     * }
     */
    private LinkedHashMap<String, String> copyFiles;
    /**
     * field_mapper = {
     * id = id
     * card = card
     * name = new_name
     * }
     */
    private LinkedHashMap<String, String> fieldMapper;

    /**
     * fields = [name, card]
     */
    private String filterFields;

    /**
     * replace_field = "name"
     * pattern = " "
     * replacement = "_"
     * is_regex = true
     */
    private String replaceField;// 替换的字段
    private String replacePattern;//支持正则，要替换的字符串
    private String replaceReplacement;//替换后的字符串
    private String replaceIsRegex; //使用正则表达式进行字符串匹配，如果为true时候，默认是全部替换，如果要只替换第一个需配置replace_first
    private String replaceFirst;//只有is_regex = true时候，此参数才会生效

    /**
     * separator = " "
     * split_field = "name"
     * output_fields = [first_name, second_name]
     */
    private String splitSeparator;
    private String splitField;
    private String splitOutputFields;

    /**
     * query = "select id, concat(name, '_') as name, age+1 as age from fake where id>0"
     */
    private String sqlQuery;

    /**
     * 随机生成字符
     * random_info_field = "name"
     * random_info_type = "1"
     */
    private String randomInfoField;
    private String randomInfoType;// "1":随机中文名 "2":随机地址 "3":随机手机号 "4":随机邮箱


    /**
     * 随机生成常量值加随机数
     * random_constant_num_field = "constant_num"
     * random_num_len = "6"
     * prefix_constant = "ABC"
     */
    private String randomConstantNumField;
    private String randomNumLen;// # 随机数长度 最大值32 默认6
    private String prefixConstant;//  常量值，可以为空

    /**
     * 随机生成时间和当前时间
     * random_time_field = "info_test_time"
     * timestamp_type = "0"
     * time_format = "yyyy-MM-dd"
     */
    private String randomTimeField;
    private String timestampType;// 0 :表示使用随机时间，1 :表示生成当前时间 默认0
    private String timeFormat;// yyyy-MM-ddHH:mm:ss  yyyy-MM-dd   HH:mm:ss  默认yyyy-MM-dd HH:mm:ss

    /**
     * 随机生成24或32位唯一ID
     * unique_id_field = "unique_id"
     * unique_id_type = "1"
     */
    private String uniqueIdField;
    private String uniqueIdType; //类型 0 、1 ; 0是24位  1是32位

    /**
     * RightJoin、InnerJoin、LeftJoin参数
     */
    private List<String> fields;
    private String fieldsString;

    private String primaryKey;
    private LinkedHashMap<String, String> joinKeys;
    private LinkedHashMap<String, String> mappingKeys = new LinkedHashMap<>();
    private String joinState;
    private String tableId;
    private List<String> masterFields;
    private String masterFieldsString;
    private List<String> addFields;
    private String addFieldsString;
    /**
     * 资产中心脱敏加密
     * Dah{
     * "source_table_name"="t1"
     * "result_table_name"="t2"
     * "dah_identifier"="sha384WithSalt"
     * "dah_column"="emp_name"
     * "dah_salt"="123456"
     * }
     **/
    private LinkedHashMap<String, String> dahDesensitization;

    private Map<String, Map<String, String>> replaceAll;
    //替换字符串组件新结构
    private List<Map<String, String>> replaceAllRewrite;

    /**
     * xmlpath用到的相关属性
     */
    // 存储xml中解析之后映射输出的字段
    private List<LinkedHashMap<String, String>> xmlMappingKeys;

    // 存储source到sink表其他字段信息
    private List<LinkedHashMap<String, String>> xmlOtherOutputFields;

    // xml 转换使用分隔符
    private String lineSplit = "#";
    /**
     * 日期类型格式（默认值:yyyy-MM-dd）
     */
    private String dateFormat;
    /**
     * 日期时间类型格式（默认值:yyyy-MM-dd HH:mm:ss）
     */
    private String datetimeFormat;


}
