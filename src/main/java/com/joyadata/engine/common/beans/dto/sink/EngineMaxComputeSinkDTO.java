package com.joyadata.engine.common.beans.dto.sink;

import com.joyadata.engine.common.beans.dto.datasource.MetadataColumnDTO;
import com.joyadata.engine.common.beans.enums.SinkPreDdlEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;


@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class EngineMaxComputeSinkDTO implements Serializable {
    /**
     * 非连接器属性： 数据源id
     */
    private String datasourceInfoId;
    /**
     * 必填项 table_name:表名，eg:test_table
     */
    private String tableName;
    /**
     * projectNmae
     */
    private String projectName;
    /**
     * 是否分区:1：分区表，0：无分区
     */
    private Integer isPartitionType;
    /**
     * 分区值 非必填 partition_spec:分区规格，eg:ds='20220101'
     */
    private String partitionValue;
    /**
     * 非必填 overwrite:是否覆盖表，默认是false
     */
    private Boolean isCoverTable = false;
    /**
     * 非连接器属性： 任务是否建表
     */
    private Boolean createAlterTable;
    /**
     * 非连接器属性:传输列
     */
    private List<MetadataColumnDTO> fieldList;
    /**
     * null，none、upper、lower
     */
    private String fieldUppLow;
    /**
     * NONE 不做处理
     * DROP 修改表名称为:DROP_YYYMMDDHHMMSS_原表名称
     * TRUNACATE 修改表名称为:TRUNACATE_YYYMMDDHHMMSS_原表名称
     */
    private SinkPreDdlEnum sinkPreDdl;
}
