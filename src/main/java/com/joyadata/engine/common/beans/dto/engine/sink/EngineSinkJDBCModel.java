package com.joyadata.engine.common.beans.dto.engine.sink;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/2/21
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class EngineSinkJDBCModel implements Serializable {
    /**
     * 目标端jdbc url
     */
    private String url;
    /**
     * 目标端jdbc 驱动
     */
    private String driver;
    /**
     * 目标端jdbc 账号
     */
    private String user;
    /**
     * 目标端jdbc 密码
     */
    private String password;
    /**
     * 目标端jdbc database
     */
    private String database;
    /**
     * 目标端jdbc 表名称
     */
    private String table;

    /**
     * 等待数据库操作完成以验证连接的时间(秒)
     */
    private String connectionCheckTimeoutSec = "30";

    /**
     * 批量条数
     */
    private String batchSize = "1024";

    /**
     * 精准一次提交，当为true时候，插入数据携带数据库事务，maxCommitAttempts会生效
     */
    private String exactlyOnce = "false";
    /**
     * 最大尝试提交次数
     * 事务提交失败时候的重试次数
     */
    private String maxCommitAttempts = "3";
    /**
     * 事务超时时间
     */
    private String transactionTimeoutSec = "-1";
    /**
     * 重试次数
     * 失败重试次数：
     */
    private String maxRetries = "0";
    /**
     * 自动提交事务
     */
    private String autoCommit = "true";
    /**
     * 通过查询主键是否存在来支持插入更新
     */
    private String supportUpsertByQueryPrimaryKeyExist = "true";
    /**
     * 如果这里有设置 ，并且supportUpsertByQueryPrimaryKeyExist 为true的时候，则目标端会做数据合并
     */
    private String primaryKeys;
    /**
     * 是否目标端做数据合并
     */
    private String enableUpsert = "false";
    /**
     * 当source_table_name未指定时，当前插件处理配置文件中前一个插件输出的dataset数据集。当指定source_table_name时，当前插件处理的是对应此参数的数据集。
     */
    private String sourceTableName;
    /**
     * 使用数据库表生成sql
     */
    private String generateSinkSql = "true";

    /**
     * 目标端字段转为大写
     */
    private String fieldIde;
    /**
     * greenplum是否使用gpload形式写入，只有gp才可以使用。gp使用pg的驱动和jdbc信息
     */
    private String useCopyStatement = "false";
    /**
     * 报错是否改为单条插入，忽略报错（错误数据不会插入），正常数据可以插入
     * stop为停止、continue 继续插入，忽略错误数据
     */
    private String pkStrategy = "stop";

    /**
     * 仅删除:only_delete_conflicting_rows
     * 仅更新:only_update_conflicting_rows
     * 先删除后插入:delete_conflicting_before_inserting_rows
     */
    private String conflictStrategyRow;

    /**
     * 连接类型
     */
    private String joyadataJoinType;
    /**
     * 连接on条件
     */
    private String joyadataJoinOns;
    /**
     * 仅插入的失败处理参数:
     */
    private String insertErrorStrategy;

    /**
     * oceanBase用来区分mysql内核和oracle内核，mysql不填，oracle填oracle
     */
    private String compatibleMode;

    //hive的认证需要的认证文件路径参数
    /**
     * hdfs-site.xml的路径，用于加载名称节点的ha配置
     */
    private String hdfsSitePath;

    /**
     * hive-site.xml的路径，用于验证配置单元元存储
     */
    private String hiveSitePath ;

    /**
     * 是否开启Kerberos认证
     */
    private String useKerberos ;

    /**
     * kerberos的krb5路径
     */
    private String krb5Path ;

    /**
     * The principal of kerberos
     */
    private String kerberosPrincipal ;

    /**
     * kerberos的keytab路径
     */
    private String kerberosKeytabPath ;
    /**
     * 空值处理
     */
    private String emptyDataStrategy;
    /**
     * 分区字段
     */
    private String partitionKeys;
}
