package com.joyadata.engine.common.beans.dto.source;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/3/2
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class EngineSourceMysqlCDCDTO implements Serializable {

    /**
     * 数据源id
     */
    private String datasourceInfoId;

    /**
     * 表id
     */
    private String tableId;


    /**
     * server id
     */
    private String serverId;

    /**
     * 表明，多个用逗号分割
     */
    //private String tableNames;

    /**
     * /**
     * CDC源的可选启动模式，有效的枚举是“initial”，“earliest”，“latest”，“timestamp”或“specific”
     */
    private String startupMode;
    /**
     * 时区
     */
    private String serverTimeZone = "GMT+08";
    /**
     * catalog 配置
     * catalog {
     * factory = MySQL
     * }
     */
    private Map<String, String> catalog;

}
