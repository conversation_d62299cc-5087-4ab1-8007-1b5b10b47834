package com.joyadata.engine.common.beans.utils.dialect.converter;

import com.alibaba.fastjson.JSON;
import com.joyadata.engine.common.beans.dto.DdlDTO;
import com.joyadata.engine.common.beans.dto.datasource.MetadataColumnDTO;
import com.joyadata.engine.common.beans.utils.dialect.AbstractDatabaseTypeConverter;
import com.joyadata.engine.common.beans.utils.dialect.paramters.DDLParameters;
import groovy.lang.Tuple2;
import groovy.lang.Tuple3;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.sql.Types;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/4/27 17:50
 */
@Slf4j
public class KuduTypeConverter extends AbstractDatabaseTypeConverter {
    @Override
    protected Tuple3<String, String, String> doGenerateDDL(DDLParameters ddlParameters) {
        String schema = ddlParameters.getSchema();
        String tableName = ddlParameters.getTableName();
        List<DdlDTO> columns = ddlParameters.getColumns();
        String tableCnName = ddlParameters.getTableCnName();
        String fieldUppLower = ddlParameters.getFieldUppLower();
        if (StringUtils.isNotBlank(fieldUppLower)) {
            tableName = getUppOrLowerCase(fieldUppLower, tableName);
        }
        List<String> list = new ArrayList<>();
        String create = "create table " + tableName + " (";
        String checkTableSql = "select 1 from " + tableName + " where 1=2";
        if (StringUtils.isNotBlank(schema)) {
            if (StringUtils.isNotBlank(fieldUppLower)) {
                schema = getUppOrLowerCase(fieldUppLower, schema);
            }
            create = "create table " + schema + "." + tableName + " (";
            checkTableSql = "select 1 from " + schema + "." + tableName + " where 1=2";
        }
        StringBuilder ddl = new StringBuilder(create);
        String finalTableName = tableName;
        String finalSchema = schema;
        columns.forEach(ddlDTO -> {
            int dataType = ddlDTO.getDataTypeJava();// Integer.valueOf(column.get("data_type"));
            int columnSize = ddlDTO.getColumnSize();// Integer.valueOf(column.get("max_length"));
            int decimalDigits = ddlDTO.getDecimalDigits(); //Integer.valueOf(column.get("decimal_digits"));
            String dataName = ddlDTO.getDataType(); //column.get("type_name");
            String columnName;
            if (StringUtils.isNotBlank(fieldUppLower)) {
                columnName = getUppOrLowerCase(fieldUppLower, ddlDTO.getColumnName());
            } else {
                columnName = ddlDTO.getColumnName();
            }
            String cnName = ddlDTO.getColumnCnName();// StringUtils.defaultString(column.get("remarks"),"");
            String dataTypeString = getKuduTypeFromSqlType(dataType, dataName, columnSize, decimalDigits);
            ddl.append("`").append(columnName).append("` ").append(dataTypeString).append(",");
            if (StringUtils.isNotBlank(cnName)) {
                cnName = cnName.replaceAll("'", "`");
                cnName = cnName.replaceAll("\"", "“");
                if (StringUtils.isNotBlank(finalSchema)) {
                    list.add("COMMENT ON COLUMN " + finalSchema + "." + finalTableName + "." + columnName + " IS '" + cnName + "'");
                } else {
                    list.add("COMMENT ON COLUMN " + finalTableName + "." + columnName + " IS '" + cnName + "'");
                }
            }
        });
        //表中文名
        if (StringUtils.isNotBlank(tableCnName)) {
            tableCnName = tableCnName.replaceAll("'", "`");
            tableCnName = tableCnName.replaceAll("\"", "“");
            if (StringUtils.isNotBlank(schema)) {
                list.add("COMMENT ON TABLE  " + schema + "." + tableName + " IS '" + tableCnName + "'");
            } else {
                list.add("COMMENT ON TABLE  " + tableName + " IS '" + tableCnName + "'");
            }
        }
        String result = ddl.toString();
        result = result.substring(0, result.length() - 1);
        result = result + ")";
        log.info("ImpalaFromMetadata生成Impala建表语句是 {}", result);
        log.info("ImpalaFromMetadata生成Impala检测表是否存在的语句是 {}", checkTableSql);
        Tuple3<String, String, String> tuple3 = new Tuple3<>(result, JSON.toJSONString(list), checkTableSql);
        return tuple3;
    }

    private static String getKuduTypeFromSqlType(int dataType, String dataName, int columnSize, int decimalDigits) {
        switch (dataType) {
            case Types.BIT:
            case Types.BOOLEAN:
            case Types.TINYINT:
            case Types.SMALLINT:
                return "SMALLINT";
            case Types.INTEGER:
                return "INT";
            case Types.BIGINT:
                return "BIGINT";
            case Types.FLOAT:
                return "FLOAT";
            case Types.REAL:
            case Types.DOUBLE:
                return "DOUBLE";
            case Types.NUMERIC:
            case Types.DECIMAL:
                if (decimalDigits == 0) {
                    if (columnSize < 3) {
                        return "SMALLINT";
                    } else if (columnSize < 5) {
                        return "MEDIUMINT";
                    } else if (columnSize < 10) {
                        return "INT";
                    } else if (columnSize < 19) {
                        return "BIGINT";
                    } else if (columnSize > 65) {
                        return "DECIMAL( 65 )";
                    } else {
                        return "DECIMAL(" + columnSize + ")";
                    }
                } else if (decimalDigits == -127) {
                    if (columnSize == 0 || columnSize > 65) {
                        return "DECIMAL(" + 65 + ", " + 0 + ")";
                    } else {
                        return "DECIMAL(" + columnSize + ", " + 0 + ")";
                    }
                } else {
                    if ("float4".equalsIgnoreCase(dataName) || "float8".equalsIgnoreCase(dataName)) {//pg字段处理
                        return "DOUBLE";
                    }
                    return "DECIMAL(" + columnSize + ", " + decimalDigits + ")";
                }
            case Types.TIME:
                return "TIME";
            case Types.DATE:
            case Types.TIMESTAMP:
                return "TIMESTAMP";
            case Types.BINARY:
            case Types.VARBINARY:
            case Types.LONGVARBINARY:
                return "STRING";
            case Types.CHAR:
            case Types.VARCHAR:
            case Types.NCHAR:
            case Types.NVARCHAR:
            case Types.LONGNVARCHAR:
            case Types.LONGVARCHAR:
                if (columnSize > 0 && columnSize <= 255) {
                    return "VARCHAR(" + columnSize + ")";
                } else {
                    return "STRING";
                }
            case Types.CLOB:
            case Types.NCLOB:
            case Types.BLOB:
                return "STRING";
            default:
                if (StringUtils.isNotBlank(dataName)) {
                    if ("NVARCHAR2".equalsIgnoreCase(dataName)) {
                        return "VARCHAR(" + columnSize + ")";
                    } else if ("VARCHAR2".equalsIgnoreCase(dataName)) {
                        return "VARCHAR(" + columnSize + ")";
                    } else if (dataName.startsWith("TIMESTAMP")) {
                        return "TIMESTAMP";
                    } else if ("INTERVAL YEAR(2) TO MONTH".equalsIgnoreCase(dataName)) {
                        return "VARCHAR(10)";
                    } else if (dataName.equalsIgnoreCase("serial")) {//pg数据库转int
                        return "INT";
                    }
                }
                return "STRING";
        }
    }

    @Override
    public Tuple2<String, List<String>> generateAddPkSql(String schema, String tableName, List<MetadataColumnDTO> pkFieldList, String fieldUppLower) {
        return new Tuple2<>("", null);
    }
}
