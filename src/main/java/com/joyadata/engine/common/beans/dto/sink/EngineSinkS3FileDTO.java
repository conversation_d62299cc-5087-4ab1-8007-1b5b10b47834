package com.joyadata.engine.common.beans.dto.sink;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @ClassName: EngineSourceOssAliFileModel
 * @date 2024/6/4
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class EngineSinkS3FileDTO {
    /**
     * 数据源id
     */
    private String datasourceInfoId;
    /**
     * 目标路径。
     */
    private String path;
    /**
     * 结果文件将首先写入tmp路径，然后使用mv将tmp目录提交到目标目录。需要一个s3目录。
     */
    private String tmpPath;
    /**
     * bucket地址，例如：s3://tyrantlucifer-image-bed
     */
    private String bucket;
    /**
     * fs3a端点
     */
    private String endpoint;
    /**
     * 验证s3a的方法。我们只支持org.apache.hadoop.fs.s3a.SimpleAWSCredentialsProvider和com.amazonaws.auth.InstanceProfileCredentialsProvider
     */
    private String fsS3aAwsCredentialsProvider;
    /**
     * 秘钥
     * 仅用于fs.s3a.aws.credentials.provider = org.apache.hadoop.fs.s3a.SimpleAWSCredentialsProvider
     */
    private String accessKey;
    /**
     * 秘钥
     * 仅用于fs.s3a.aws.credentials.provider = org.apache.hadoop.fs.s3a.SimpleAWSCredentialsProvider
     */
    private String accessSecret;
    /**
     * 是否需要自定义文件名
     */
    private String customFilename;
    /**
     * 仅当custom_filename为true时使用 file_name_expression描述将创建到路径中的文件表达式
     */
    private String fileNameExpression;
    /**
     * 仅当custom_filename为true时使用 当file_name_expression参数中的格式为xxxx-${Now}时，filename_time_format可以指定路径的时间格式，默认值为yyyy。MM.dd
     */
    private String filenameTimeFormat;
    /**
     * text json csv orc parquet excel
     * 请注意，最终文件名将以file_format_type的后缀结尾，文本文件的后缀为txt。
     */
    private String fileFormatType;
    /**
     * 仅当file_format_type为文本时使用
     */
    private String fieldDelimiter;
    /**
     * 仅当file_format_type为文本时使用
     */
    private String rowDelimiter;
    /**
     * 是否需要处理分区。
     */
    private String havePartition;
    /**
     * 只有在have_partition为true时才使用
     */
    private List<String> partitionBy;
    /**
     * 只有在have_partition为true时才使用
     */
    private String partitionDirExpression;
    /**
     * 只有在have_partition为true时才使用
     */
    private String isPartitionFieldWriteInFile;
    /**
     * 当此参数为空时，所有字段都是汇点列
     */
    private List<String> sinkColumns;
    /**
     * Only support true now.
     */
    private String isEnableTransaction;
    /**
     * 文件中的最大行数。
     */
    private String batchSize;
    /**
     *
     */
    private String compressCodec;
    /**
     * 仅当file_format_type为excel时使用。
     */
    private String maxRowsInMemory;
    /**
     * 仅当file_format_type为excel时使用。
     */
    private String sheetName;
    /**
     * 在开启同步任务之前，对目标路径进行不同的处理
     * RECREATE_SCHEMA ：路径不存在时创建。如果路径已经存在，请删除该路径后重新创建.
     * CREATE_SCHEMA_WHEN_NOT_EXIST 当路径不存在时创建，当路径存在时使用。
     * ERROR_WHEN_SCHEMA_NOT_EXIST ：当路径不存在时，将报告错误
     */
    private String schemaSaveMode;
    /**
     * 打开同步任务前，对目标路径下的数据文件进行不同的处理
     * DROP_DATA： 使用该路径，但删除该路径下的数据文件。
     * APPEND_DATA:使用该路径，并在该路径中添加新文件用于写入数据。
     * ERROR_WHEN_DATA_EXISTS：当路径中有一些数据文件时，将报告错误。
     */
    private String dataSaveMode;

    /**
     * 数据加载方式默认S3
     * S3
     * ICFSDOS
     */
    private String loadType = "S3";
    /**
     * 校验文件
     */
    private String validateFile;
    /**
     * 校验内容
     */
    private String validateContent;
    /**
     * 空值处理
     */
    private String emptyDataStrategy;
    /**
     * YYYY_MM_DD_HH_MM_SS("yyyy-MM-dd HH:mm:ss"),
     * YYYY_MM_DD_HH_MM_SS_SSSSSS("yyyy-MM-dd HH:mm:ss.SSSSSS"),
     * YYYY_MM_DD_HH_MM_SS_SPOT("yyyy.MM.dd HH:mm:ss"),
     * YYYY_MM_DD_HH_MM_SS_SLASH("yyyy/MM/dd HH:mm:ss"),
     * YYYY_MM_DD_HH_MM_SS_NO_SPLIT("yyyyMMddHHmmss"),
     * YYYY_MM_DD_HH_MM_SS_ISO8601("yyyy-MM-dd'T'HH:mm:ss"),
     * YYYY_MM_DD_HH_MM_SS_SSS_ISO8601("yyyy-MM-dd'T'HH:mm:ss.SSS"),
     * YYYY_MM_DD_HH_MM_SS_SSSSSS_ISO8601("yyyy-MM-dd'T'HH:mm:ss.SSSSSS"),
     * YYYY_MM_DD_HH_MM_SS_SSSSSSSSS_ISO8601("yyyy-MM-dd'T'HH:mm:ss.SSSSSSSSS");
     */
    private String datetimeFormat = "yyyy-MM-dd HH:mm:ss";

    /**
     * YYYY_MM_DD("yyyy-MM-dd"),
     * YY_MM_DD("yy-MM-dd"),
     * YYYY_MM_DD_SPOT("yyyy.MM.dd"),
     * YYYY_MM_DD_SLASH("yyyy/MM/dd");
     */
    private String dateFormat = "yyyy-MM-dd";
    /**
     * HH_MM_SS("HH:mm:ss"),
     * HH_MM_SS_SSS("HH:mm:ss.SSS");
     */
    private String timeFormat = "HH:mm:ss";
    /**
     * 校验文件是否根据真实数据文件生成多个
     * true：生成多个
     * false: 生成单个
     */
    private boolean validates = false;
    private boolean cleanTargetFolder = false;
    private String encoding;
}
