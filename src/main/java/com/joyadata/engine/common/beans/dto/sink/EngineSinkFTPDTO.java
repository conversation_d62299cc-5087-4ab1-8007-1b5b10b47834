package com.joyadata.engine.common.beans.dto.sink;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2024/5/28 10:21.
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class EngineSinkFTPDTO implements Serializable {
    /**
     * 文件名称，要将数据合并写入到一个文件时传这个名字
     */
    private String fileName;
    /**
     * 数据源id
     */
    private String datasourceInfoId;
    /**
     * 服务器ip
     */
    private String host;
    /**
     * 服务器端口
     */
    private String port;
    /**
     * 服务器用户名
     */
    private String user;
    /**
     * 服务器密码
     */
    private String password;
    /**
     * 文件路径
     */
    private String path;
    /**
     * The result file will write to a tmp path first and then use mv to submit tmp dir to target dir. Need a FTP dir.
     * default value:/tmp/seatunnel
     * 目标服务器的临时路径
     */
    private String tmpPath;
    /**
     * 连接模式
     * The target ftp connection mode , default is active mode, supported as the following modes:
     * active_local passive_local
     */
    private String connectionMode;
    /**
     * Whether you need custom the filename
     * default value: false
     * 是否要制定目标端的文件名称
     */
    private String customFilename;
    /**
     * 仅当custom_filename为true时使用
     * file_name_expression描述将创建到路径中的文件表达式。我们可以在file_name_expression中添加变量${now}或${uuid}，如test_${uuid}_$｛now｝，$｛now｝表示当前时间，其格式可以通过指定选项filename_time_format来定义。
     * 请注意，如果is_enable_transaction为true，我们将自动添加${transactionId}_在文件的头中。
     * <p>
     * Only used when custom_filename is true
     * default value: "${transactionId}"
     * 设置的文件名称，需要和customFilename配合使用
     */
    private String fileNameExpression;
    /**
     * 仅当custom_filename为true时使用
     * 当file_name_expression参数中的格式为xxxx-${now}时，filename_time_format可以指定路径的时间格式
     * default value is yyyy.MM.dd
     */
    private String filenameTimeFormat;
    /**
     * 文件类型
     */
    private String fileFormatType;
    /**
     * 数据分隔符
     * Only used when file_format_type is text
     */
    private String fieldDelimiter;
    /**
     * 每行的数据分隔符
     * Only used when file_format_type is text
     */
    private String rowDelimiter;
    /**
     * 是否需要处理分区。
     * Whether you need processing partitions.
     */
    private String havePartition;
    /**
     * 根据所选字段对数据进行分区。
     * Only used then have_partition is true
     */
    private List<String> partitionBy;
    /**
     * 如果指定了partition_by，我们将根据分区信息生成相应的分区目录，最终文件将放置在分区目录中。
     * Only used then have_partition is true
     */
    private String partitionDirExpression;
    /**
     * If is_partition_field_write_in_file is true, the partition field and the value of it will be write into data file.
     * For example, if you want to write a Hive Data File, Its value should be false.
     * 如果is_partition_file_write_in_file为true，则分区字段及其值将写入数据文件。
     * 例如，如果要编写配置单元数据文件，其值应为false。
     * Only used then have_partition is true
     */
    private String isPartitionFieldWriteInFile;
    /**
     * When this parameter is empty, all fields are sink columns
     * 当此参数为空时，所有字段都是汇点列
     */
    private List<String> sinkColumns;

    private boolean enableTransaction = true;
    /**
     * 文件中的最大行数。对于SeaTunnel Engine，文件中的行数由batch_size和checkpoint.interval共同决定。
     * 如果checkpoint.interval的值足够大，则接收器写入程序将在文件中写入行，直到文件中的行大于batch_size。
     * 如果checkpoint.interval很小，则当触发新的检查点时，接收器写入程序将创建一个新文件。
     */
    private String batchSize;
    /**
     * The compress codec of files and the details that supported as the following shown:
     * <p>
     * txt: lzo none
     * json: lzo none
     * csv: lzo none
     * orc: lzo snappy lz4 zlib none
     * parquet: lzo snappy lz4 gzip brotli zstd none
     * Tips: excel type does not support any compression format
     */
    private String compressCodec;
    /**
     * Reader the sheet of the workbook,Only used when file_format is excel.
     */
    private String sheetName;
    /**
     * 当文件格式为Excel时，可以缓存在内存中的最大数据项数。
     * When File Format is Excel,The maximum number of data items that can be cached in the memory.
     */
    private String maxRowsInMemory;
    /**
     * 空值处理
     */
    private String emptyDataStrategy;

    /**
     * YYYY_MM_DD_HH_MM_SS("yyyy-MM-dd HH:mm:ss"),
     * YYYY_MM_DD_HH_MM_SS_SSSSSS("yyyy-MM-dd HH:mm:ss.SSSSSS"),
     * YYYY_MM_DD_HH_MM_SS_SPOT("yyyy.MM.dd HH:mm:ss"),
     * YYYY_MM_DD_HH_MM_SS_SLASH("yyyy/MM/dd HH:mm:ss"),
     * YYYY_MM_DD_HH_MM_SS_NO_SPLIT("yyyyMMddHHmmss"),
     * YYYY_MM_DD_HH_MM_SS_ISO8601("yyyy-MM-dd'T'HH:mm:ss"),
     * YYYY_MM_DD_HH_MM_SS_SSS_ISO8601("yyyy-MM-dd'T'HH:mm:ss.SSS"),
     * YYYY_MM_DD_HH_MM_SS_SSSSSS_ISO8601("yyyy-MM-dd'T'HH:mm:ss.SSSSSS"),
     * YYYY_MM_DD_HH_MM_SS_SSSSSSSSS_ISO8601("yyyy-MM-dd'T'HH:mm:ss.SSSSSSSSS");
     */
    private String datetimeFormat = "yyyy-MM-dd HH:mm:ss";

    /**
     * YYYY_MM_DD("yyyy-MM-dd"),
     * YY_MM_DD("yy-MM-dd"),
     * YYYY_MM_DD_SPOT("yyyy.MM.dd"),
     * YYYY_MM_DD_SLASH("yyyy/MM/dd");
     */
    private String dateFormat = "yyyy-MM-dd";
    /**
     * HH_MM_SS("HH:mm:ss"),
     * HH_MM_SS_SSS("HH:mm:ss.SSS");
     */
    private String timeFormat = "HH:mm:ss";
    private String encoding;
    /**
     * 校验文件
     */
    private String validateFile;
    /**
     * 校验内容
     */
    private String validateContent;
}
