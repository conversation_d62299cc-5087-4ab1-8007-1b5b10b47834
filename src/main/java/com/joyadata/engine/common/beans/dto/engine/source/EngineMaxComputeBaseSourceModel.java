package com.joyadata.engine.common.beans.dto.engine.source;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.LinkedHashMap;

@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class EngineMaxComputeBaseSourceModel implements Serializable {

    /**
     * 必填项 accessId:入口id，eg:LTAI5tAr5vXokEu4EA7t8vP9
     * */
    private String accessId;
    /**
     * 必填项 accessKey:密钥，eg:******************************
     * */
    private String accessKey;
    /**
     * 必填项 endpoint：请求url，eg:<a href="https://xxxe.aliyun.com/api">...</a>
     * */
    private String endpoint;
    /**
     * endpoint：请求url，eg:<a href="https://xxxe.aliyun.com/api">...</a>
     */
    private String tunnelEndpoint;
    /**
     * 必填项 project:项目名称，在阿里云上创建的项目名称，eg:test_pro
     * */
    private String project;
    /**
     * 必填项 table_name:表名，eg:test_table
     * */
    private String tableName;
    /**
     * 非必填 partition_spec:分区规格，eg:ds='20220101'
     * */
    private String partitionSpec;
    /**
     * 非必填 split_row:分片行数，默认10000行
     * */
    private Integer splitRow = 10000;
    /**
     * 非必填 parallelism:并行度
     * */
    private Integer parallelism;
    /**
     * 非必填 result_table_name:结果表名，用于连接sink或者transform，eg:fake
     * */
    private String resultTableName;
    /**
     * 非必填 schema:上游数据的架构信息
     * */
    private LinkedHashMap<String, String> schema;

}
