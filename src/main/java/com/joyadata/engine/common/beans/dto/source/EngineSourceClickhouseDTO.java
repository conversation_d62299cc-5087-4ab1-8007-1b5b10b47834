package com.joyadata.engine.common.beans.dto.source;

import com.joyadata.engine.common.beans.dto.RowFilterDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/3/2
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class EngineSourceClickhouseDTO extends EngineSourceDTO implements Serializable {

    /**
     * 数据源id
     */
    private String datasourceInfoId;

    /**
     * 表id
     */
    private String tableId;

    /**
     * sql语句(sql查询的列 要和fieldList字段对应)
     */
    private String query;
    /**
     * where条件
     */
    private List<RowFilterDTO> whereCondition;

    /**
     * 用户自定义where
     */
    private String userDefineWhere;

    /**
     * 增量字段
     */
    private String incrField;
    /**
     * 并行读取拆分字段
     */
    private String partitionColumn;
    /**
     * 分片数量 设置增量字段必须设置分片数量
     */
    private String partitionNum;
    /**
     * 前置SQL
     */
    private List<String> preSql;
    /**
     * 后置SQL
     */
    private List<String> postSql;
    /**
     * 自定义SQL条件
     */
    private String customWhereSql;

    /**
     * 并发度
     */
    private Integer parallelism;
}
