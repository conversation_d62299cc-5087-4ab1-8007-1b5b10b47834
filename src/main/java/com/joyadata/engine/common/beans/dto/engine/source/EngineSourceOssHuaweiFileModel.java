package com.joyadata.engine.common.beans.dto.engine.source;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.LinkedHashMap;

/**
 * <AUTHOR>
 * @version V1.0
 * @ClassName: EngineSourceOssAliFileModel
 * @date 2024/6/4
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class EngineSourceOssHuaweiFileModel {
    /**
     * 如果未指定`result_table_name`，则该插件处理的数据不会注册为可被其他插件直接访问的数据集`(dataStream/dataset)`，
     * 也称为临时表`(table)`；如果指定了`result_table_name`，则该插件处理的数据会注册为可被其他插件直接访问的数据集`(dataStream/dataset)`，
     * 也称为临时表`(table)`。这里注册的数据集`(dataStream/dataset)`，其他插件通过指定`source_table_name`可直接进行访问。
     * https://seatunnel.apache.org/docs/2.3.3/connector-v2/source/common-options
     */
    private String resultTableName;
    /**
     *
     */
    private String path;
    /**
     * oss文件系统的bucket地址，例如：oss://tyrantlucifer-image-bed
     */
    private String bucket;
    /**
     * oss文件系统的访问密钥。
     */
    private String accessKey;
    /**
     * oss文件系统的访问秘钥。
     */
    private String accessSecret;
    /**
     * oss文件系统的端点。
     */
    private String endpoint;
    /**
     * 源端的读列列表，可以用它来实现字段投影，如果用户希望在读取文本json csv文件时使用此功能，则必须配置“schema”选项。
     */
    private String readColumns;

    /**
     * text json csv orc parquet excel
     * 请注意，最终文件名将以file_format_type的后缀结尾，文本文件的后缀为txt。
     */
    private String fileFormatType;
    /**
     * 仅当file_format_type为文本时使用
     */
    private String fieldDelimiter;
    /**
     * Control whether parse the partition keys and values from file path
     * For example if you read a file from path oss://hadoop-cluster/tmp/seatunnel/parquet/name=tyrantlucifer/age=26
     * Every record data from file will be added these two fields:
     * name age
     * tyrantlucifer 26
     * Tips: Do not define partition fields in schema option
     */
    private String parsePartitionFromPath;
    /**
     * Date type format, used to tell connector how to convert string to date,supported as the following formats:yyyy-MM-dd yyyy.MM.dd yyyy/MM/dddefault yyyy-MM-dd
     * 日期类型格式，用于告诉连接器如何将字符串转换为日期，支持如下格式:yyyy-MM-dd yyyy.MM.dd yyyy/MM/dddefault yyyy-MM-dd
     */
    private String dateFormat;

    /**
     * Datetime type format, used to tell connector how to convert string to datetime, supported as the following formats:yyyy-MM-dd HH:mm:ss yyyy.MM.dd HH:mm:ss yyyy/MM/dd HH:mm:ssyyyyMMddHHmmssdefault yyyy-MM-dd HH:mm:ss
     * 日期时间类型格式，用于告诉连接器如何将字符串转换为日期时间，支持如下格式:yyyy- mm -dd HH:mm:ssyyyy . mm .dd HH:mm:ssyyyy / mm /dd HH:mm:ssyyyyMMddHHmmssdefault yyyy- mm -dd HH:mm:ss
     */
    private String datetimeFormat;
    /**
     * 时间类型格式，用于告诉连接器如何将字符串转换为时间，支持以下格式:
     * HH:mm:ss HH:mm:ss.SSS
     * default HH:mm:ss
     */
    private String timeFormat;
    /**
     * Skip the first few lines, but only for the txt and csv.
     * For example, set like following:
     * skip_header_row_number = 2
     * then SeaTunnel will skip the first 2 lines from source files
     * 跳过前几行，但仅限于txt和csv文件。例如:skip_header_row_number = 2。然后SeaTunnel将跳过源文件的前两行
     */
    private String skipHeaderRowNumber;
    /**
     * The schema of upstream data.
     */
    private LinkedHashMap<String, String> schema;
    /**
     *
     */
    private String compressCodec;
    /**
     * 过滤模式，用于过滤文件,*.txt表示您只需要读取以.txt结尾的文件
     */
    private String fileFilterPattern;
    /**
     * 仅当file_format_type为excel时使用。
     */
    private String sheetName;
    private String encoding;
}
