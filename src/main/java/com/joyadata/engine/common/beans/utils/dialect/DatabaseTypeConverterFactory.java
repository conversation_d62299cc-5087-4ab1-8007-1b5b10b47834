package com.joyadata.engine.common.beans.utils.dialect;

import com.joyadata.engine.common.beans.utils.dialect.converter.ClickhouseTypeConverter;
import com.joyadata.engine.common.beans.utils.dialect.converter.DB2TypeConverter;
import com.joyadata.engine.common.beans.utils.dialect.converter.DmdbTypeConverter;
import com.joyadata.engine.common.beans.utils.dialect.converter.DorisTypeConverter;
import com.joyadata.engine.common.beans.utils.dialect.converter.DwsPgTypeConverter;
import com.joyadata.engine.common.beans.utils.dialect.converter.GaussDb200TypeConverter;
import com.joyadata.engine.common.beans.utils.dialect.converter.GbaseTypeConverter;
import com.joyadata.engine.common.beans.utils.dialect.converter.HiveTypeConverter;
import com.joyadata.engine.common.beans.utils.dialect.converter.ImpalaTypeConverter;
import com.joyadata.engine.common.beans.utils.dialect.converter.InformixTypeConverter;
import com.joyadata.engine.common.beans.utils.dialect.converter.Kingbase8TypeConverter;
import com.joyadata.engine.common.beans.utils.dialect.converter.KuduTypeConverter;
import com.joyadata.engine.common.beans.utils.dialect.converter.MppTypeConverter;
import com.joyadata.engine.common.beans.utils.dialect.converter.MySQLTypeConverter;
import com.joyadata.engine.common.beans.utils.dialect.converter.OceanbaseMysqlTypeConverter;
import com.joyadata.engine.common.beans.utils.dialect.converter.OceanbaseOracleTypeConverter;
import com.joyadata.engine.common.beans.utils.dialect.converter.OracleTypeConverter;
import com.joyadata.engine.common.beans.utils.dialect.converter.PostgresqlTypeConverter;
import com.joyadata.engine.common.beans.utils.dialect.converter.SapHanaTypeConverter;
import com.joyadata.engine.common.beans.utils.dialect.converter.SqlserverTypeConverter;
import com.joyadata.engine.common.beans.utils.dialect.converter.StarrocksTypeConverter;
import com.joyadata.engine.common.beans.utils.dialect.converter.SysbaseTypeConverter;
import com.joyadata.engine.common.beans.utils.dialect.converter.TbaseTypeConverter;
import com.joyadata.engine.common.beans.utils.dialect.converter.TdsqlForMysqlTypeConverter;
import com.joyadata.engine.common.beans.utils.dialect.converter.TdsqlForPgTypeConverter;
import com.joyadata.engine.common.beans.utils.dialect.converter.TiDBTypeConverter;
import com.joyadata.engine.common.beans.utils.dialect.converter.MaxComputeTypeConverter;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;

/**
 * 数据库类型转换器工厂
 * 支持多种数据库类型的DDL生成和主键SQL生成
 *
 * <AUTHOR>
 * @date 2024/2/20
 */
@Slf4j
public class DatabaseTypeConverterFactory {

    private static final Map<String, DatabaseTypeConverter> CONVERTER_MAP = new HashMap<>();

    // 静态初始化块，在类加载时执行
    static {
        initializeConverters();
    }

    /**
     * 初始化所有数据库类型转换器
     */
    private static void initializeConverters() {
        // MySQL系列
        registerConverter("mysql", new MySQLTypeConverter());
        registerConverter("goldendb", new MySQLTypeConverter());
        registerConverter("oceanbase_for_mysql", new MySQLTypeConverter());
        registerConverter("gaussdb_for_mysql", new MySQLTypeConverter());

        // Oracle系列
        registerConverter("oracle", new OracleTypeConverter());
        registerConverter("oceanbase_for_oracle", new OracleTypeConverter());

        // SQL Server
        registerConverter("sqlserver", new SqlserverTypeConverter());

        // PostgreSQL系列
        registerConverter("postgresql", new PostgresqlTypeConverter());
        registerConverter("greenplum", new PostgresqlTypeConverter());
        registerConverter("gaussdb", new PostgresqlTypeConverter());
        registerConverter("analyticdb_postgresql", new PostgresqlTypeConverter());
        registerConverter("greenplum_postgresql", new PostgresqlTypeConverter());

        // 其他数据库
        registerConverter("gbase", new GbaseTypeConverter());
        registerConverter("dmdb", new DmdbTypeConverter());
        registerConverter("mpp", new MppTypeConverter());
        registerConverter("kingbase8", new Kingbase8TypeConverter());
        registerConverter("sap_hana", new SapHanaTypeConverter());

        // 大数据平台
        registerConverter("hive", new HiveTypeConverter());
        registerConverter("doris", new DorisTypeConverter());
        registerConverter("inceptor", new HiveTypeConverter());
        registerConverter("maxcompute", new MaxComputeTypeConverter());

        // 云数据库
        registerConverter("oceanbase_mysql", new OceanbaseMysqlTypeConverter());
        registerConverter("oceanbase_oracle", new OceanbaseOracleTypeConverter());
        registerConverter("tidb", new TiDBTypeConverter());

        // 其他
        registerConverter("impala", new ImpalaTypeConverter());
        registerConverter("db2", new DB2TypeConverter());
        registerConverter("clickhouse", new ClickhouseTypeConverter());
        registerConverter("starrocks", new StarrocksTypeConverter());
        registerConverter("dws_pg", new DwsPgTypeConverter());
        registerConverter("tbase", new TbaseTypeConverter());
        registerConverter("tdsql_for_pg", new TdsqlForPgTypeConverter());
        registerConverter("tdsql_for_oracle", new TdsqlForPgTypeConverter());
        registerConverter("tdsql_for_mysql", new TdsqlForMysqlTypeConverter());
        registerConverter("gauss_db200", new GaussDb200TypeConverter());
        registerConverter("informix", new InformixTypeConverter());
        registerConverter("kudu", new KuduTypeConverter());
        registerConverter("sybase", new SysbaseTypeConverter());

        log.info("DatabaseTypeConverterFactory initialized with {} converters", CONVERTER_MAP.size());
    }

    /**
     * 注册数据库类型转换器
     *
     * @param type      数据库类型
     * @param converter 转换器实例
     */
    private static void registerConverter(String type, DatabaseTypeConverter converter) {
        if (CONVERTER_MAP.containsKey(type)) {
            log.warn("Duplicate database type converter found for type: {}", type);
        }
        CONVERTER_MAP.put(type.toLowerCase(), converter);
        log.debug("Registered database type converter: {}", type);
    }

    /**
     * 获取数据库类型转换器
     *
     * @param databaseType 数据库类型
     * @return 对应的转换器实例
     * @throws IllegalArgumentException 当不支持的数据库类型时抛出
     */
    public static DatabaseTypeConverter getConverter(String databaseType) {
        if (databaseType == null || databaseType.trim().isEmpty()) {
            throw new IllegalArgumentException("Database type cannot be null or empty");
        }

        DatabaseTypeConverter converter = CONVERTER_MAP.get(databaseType.toLowerCase());
        if (converter == null) {
            throw new IllegalArgumentException("Unsupported database type: " + databaseType +
                    ". Supported types: " + String.join(", ", CONVERTER_MAP.keySet()));
        }
        return converter;
    }

    /**
     * 检查是否支持指定的数据库类型
     *
     * @param databaseType 数据库类型
     * @return 是否支持
     */
    public static boolean isSupported(String databaseType) {
        if (databaseType == null || databaseType.trim().isEmpty()) {
            return false;
        }
        return CONVERTER_MAP.containsKey(databaseType.toLowerCase());
    }

    /**
     * 获取所有支持的数据库类型
     *
     * @return 支持的数据库类型集合
     */
    public static java.util.Set<String> getSupportedTypes() {
        return new java.util.HashSet<>(CONVERTER_MAP.keySet());
    }

    /**
     * 手动注册自定义转换器
     * 注意：此方法主要用于扩展功能，不建议在生产环境中频繁调用
     *
     * @param type      数据库类型
     * @param converter 转换器实例
     */
    public static void registerCustomConverter(String type, DatabaseTypeConverter converter) {
        if (type == null || type.trim().isEmpty()) {
            throw new IllegalArgumentException("Database type cannot be null or empty");
        }
        if (converter == null) {
            throw new IllegalArgumentException("Converter cannot be null");
        }

        registerConverter(type, converter);
        log.info("Registered custom database type converter: {}", type);
    }
}