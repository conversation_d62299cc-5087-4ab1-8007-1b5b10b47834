package com.joyadata.engine.common.beans.dto.source;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @desc 添加MongoDB的源信息框架
 * @date 2024/3/14
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class EngineSourceMongoDBDTO extends EngineBaseSourceDTO implements Serializable {

    /**
     * 集合-表名
     */
    private String tableId;

    /**
     * 条件过滤
     */
    private String query;

    /**
     * 字段筛选
     */
    private String projection;

    /**
     * 拆分字段
     */
    private String partitionSplitKey;

    /**
     * 拆分大小
     */
    private Long partitionSplitSize;

    /**
     * 超时设置
     */
    private Boolean cursorNoTimeout;

    /**
     * 每次抽取批量获取的条数
     */
    private String fetchSize;

    /**
     * This parameter is a MongoDB query option that limits the maximum execution time for query operations. The value of maxTimeMin is in Minute. If the execution time of the query exceeds the specified time limit, MongoDB will terminate the operation and return an error.
     */
    private String maxTimeMin;

    /**
     * By utilizing flatSyncString, only one field attribute value can be set, and the field type must be a String. This operation will perform a string mapping on a single MongoDB data entry.
     */
    private Boolean flatSyncString;

}
