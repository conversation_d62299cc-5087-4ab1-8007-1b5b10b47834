package com.joyadata.engine.common.beans.dto.engine.sink;

import com.joyadata.engine.common.beans.dto.sink.EngineBaseSinkDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.LinkedHashMap;
import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2024/9/26 11:09.
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@EqualsAndHashCode(callSuper = true)
@Data
public class EngineSinkHbaseModel extends EngineBaseSinkModel {

    /**
     * zookeeper_quorum
     */
    private String zookeeperQuorum;
    /**
     * 表名
     */
    private String tableName;

    /**
     * query_columns
     */
    private String rowkeyColumn;

    /**
     * The delimiter of joining multi row keys, default ""
     */
    private String rowkeyDelimiter;
    /**
     * query_columns
     */
    private List<String> familyName;

    /**
     * The version column name, you can use it to assign timestamp for hbase record
     */
    private String versionColumn;

    /**
     * The mode of writing null value, support [skip, empty], default skip
     *
     * skip: When the field is null, connector will not write this field to hbase
     * empty: When the field is null, connector will write generate empty value for this field
     */
    private String nullMode;

    /**
     The wal log write flag, default false
     */
    private String walWrite;

    /**
     The write buffer size of hbase client, default 8 * 1024 * 1024
     */
    private String writeBufferSize;

    /**
     The encoding of string field, support [utf8, gbk], default utf8
     */
    private String encoding;

    /**
     The extra configuration of hbase
     */
    private String hbaseExtraConfig;

    /**
     * kerberos认证的文件的所属路径
     */
    private String filePath;

    /**
     * kerberos认证的用户名
     */
    private String user;

    /**
     * serverPrincipal
     */
    private String serverPrincipal;
}
