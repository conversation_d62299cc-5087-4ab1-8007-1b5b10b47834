package com.joyadata.engine.common.beans.enums;

/**
 * <AUTHOR>
 * @date 2024/2/20
 */

public enum DatabaseTypeEnum {
    MYSQL("mysql", "com.mysql.cj.jdbc.Driver"),
    ORACLE("oracle", "oracle.jdbc.driver.OracleDriver"),
    SQLSERVER("sqlserver", "com.microsoft.sqlserver.jdbc.SQLServerDriver"),
    SYBASE("sybase", "com.sybase.jdbc4.jdbc.SybDriver"),
    POSTGRESQL("postgresql", "org.postgresql.Driver"),
    DMDB("dmdb", "dm.jdbc.driver.DmDriver"),
    GAUSSDB("gaussdb", "org.postgresql.Driver"),
    /*DB2("db2", "com.ibm.db2.jdbc.app.DB2Drive"),*/
    DB2("db2", "com.ibm.db2.jcc.DB2Driver"),
    MPP("mpp", "org.postgresql.Driver"),
    HIVE("hive", "org.apache.hive.jdbc.HiveDriver"),
    INCEPTOR("inceptor", "org.apache.hive.jdbc.HiveDriver"),
    ELASTICSEARCH("elasticsearch", ""),
    CLICKHOUSE("clickhouse", "ru.yandex.clickhouse.ClickHouseDriver"),
    KINGBASE8("kingbase8", "com.kingbase8.Driver"),
    OCEANBASE("oceanbase", "com.oceanbase.jdbc.Driver"),
    GREENPLUM("greenplum", "com.pivotal.jdbc.GreenplumDriver"),
    GBASE("gbase", "com.gbase.jdbc.Driver"),
    STARROCKS("starrocks", "com.mysql.jdbc.Driver"),
    SAP_HANA("sap_hana", "com.sap.db.jdbc.Driver"),
    KUDU("kudu", ""),
    MONGODB("mongodb", ""),
    AnalyticDB_PostgreSQL("analyticdb_postgresql", "org.postgresql.Driver"),
    ArgoDB("argodb", "org.apache.hive.jdbc.HiveDriver"),
    HDFS("hdfs", ""),
    OSS_ALI("ali_oss", ""),
    SFTPFILE("sftp", ""),
    FTPFILE("ftp", ""),
    TiDB("tidb", "com.mysql.jdbc.Driver"),
    DORIS("Doris", "com.mysql.jdbc.Driver"),
    OSS_HUAWEI("oss_huawei", ""),
    DataHub("DataHub", ""),
    IMPALA("impala", "com.cloudera.impala.jdbc41.Driver"),
    ADB_GPDIST("analyticdb_postgresql", "org.postgresql.Driver"),
    ARGO_HDFS_FILE("argodb", ""),
    GOLDENDB("GoldenDB", "com.goldendb.jdbc.Driver"),
    DWS_PG("dws_pg", "org.postgresql.Driver"),
    TDSQL_FOR_MYSQL("tdsql_for_mysql", "com.mysql.jdbc.Driver"),
    TDSQL_FOR_PG("tdsql_for_pg", "org.postgresql.Driver"),
    TDSQL_FOR_ORACLE("tdsql_for_oracle", "org.postgresql.Driver"),
    TBASE("tbase", "org.postgresql.Driver"),
    GAUSS_DB200("gauss_db200", "com.huawei.gauss200.jdbc.Driver"),
    HBASE("hbase", ""),
    GREENPLUM_POSTGRESQL("greenplum_postgresql", "com.pivotal.jdbc.GreenplumDriver"),
    OCEANBASE_FOR_MYSQL("oceanbase_for_mysql", "com.alipay.oceanbase.jdbc.Driver"),
    OCEANBASE_FOR_ORACLE("oceanbase_for_oracle", "com.alipay.oceanbase.jdbc.Driver"),
    AWS_S3("aws_s3", ""),
    INSPUR_S3("inspur_s3", ""),
    INSPUR_OSS("inspur_oss", ""),
    INFORMIX("informix", "com.informix.jdbc.IfxDriver"),
    GAUSSDB_FOR_MYSQL("gaussdb_for_mysql", "com.mysql.jdbc.Driver"),
    MAXCOMPUTE("maxcompute", "");
    private String name;
    private String driver;

    DatabaseTypeEnum() {
    }

    public static DatabaseTypeEnum getDatabaseTypeEnum(String name) {
        DatabaseTypeEnum[] databaseTypeEnums = DatabaseTypeEnum.values();
        for (DatabaseTypeEnum databaseTypeEnum : databaseTypeEnums) {
            if (databaseTypeEnum.name.equalsIgnoreCase(name)) {
                return databaseTypeEnum;
            }
        }
        return null;
    }

    DatabaseTypeEnum(String name, String driver) {
        this.name = name;
        this.driver = driver;
    }

    public String getName() {
        return name;
    }

    public String getDriver() {
        return driver;
    }
}
