package com.joyadata.engine.common.beans.dto.sink;

import com.joyadata.engine.common.beans.dto.datasource.MetadataColumnDTO;
import com.joyadata.engine.common.beans.enums.ConflictStrategyRowEnum;
import com.joyadata.engine.common.beans.enums.HiveStorageTypeEnum;
import com.joyadata.engine.common.beans.enums.JoyadataJoinType;
import com.joyadata.engine.common.beans.enums.PrimaryKeyModelEnum;
import com.joyadata.engine.common.beans.enums.SinkPreDdlEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/3/1
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class EngineSinkJDBCDTO implements Serializable {
    /**
     * 数据源id
     */
    private String datasourceInfoId;
    /**
     * 写入表名称
     */
    private String tableName;

    /**
     * 优先拿此字段值，如果此字段值为空，则取元数据中的主键信息
     * 默认是支持目标端数据更新的，当此字段不为空时
     * 主键更新，多个主键["a","b"]
     */
    private List<String> primaryKeys;


    /**
     * 是否目标端做数据合并 对应supportUpsertByQueryPrimaryKeyExist
     */
    private String merge = "false";

    /**
     * 是否目标端做数据合并
     */
    private String enableUpsert = "false";
    /**
     * 前置SQL
     */
    private List<String> preSql;
    /**
     * 后置SQL
     */
    private List<String> postSql;

    private List<String> indexSql;
    /**
     * NONE 不做处理
     * DROP 修改表名称为:DROP_YYYMMDDHHMMSS_原表名称
     * TRUNACATE 修改表名称为:TRUNACATE_YYYMMDDHHMMSS_原表名称
     */
    private SinkPreDdlEnum sinkPreDdl;
    /**
     * 失败重试次数
     */
    private Integer maxRetries;
    /**
     * 批量写入最大行数
     */
    private Integer batchSize;
    /**
     * 传输列
     */
    private List<MetadataColumnDTO> fieldList;

    /**
     * greenplum是否使用gpload形式写入，只有gp才可以使用。gp使用pg的驱动和jdbc信息
     */
    private String useCopyStatement = "false";
    /**
     * 报错是否改为单条插入，忽略报错（错误数据不会插入），正常数据可以插入
     * stop为停止、continue 继续插入，忽略错误数据
     */
    private String pkStrategy = "stop";

    /**
     * 仅删除:only_delete_conflicting_rows
     * 仅更新:only_update_conflicting_rows
     * 先删除后插入:delete_conflicting_before_inserting_rows
     */
    private ConflictStrategyRowEnum conflictStrategyRow;

    /**
     * 连接类型
     */
    private JoyadataJoinType joyadataJoinType;
    /**
     * 连接on条件
     */
    private List<String> joyadataJoinOns;
    /**
     * 库名
     */
    private String dbName;
    /**
     * 模式名
     */
    private String schemaName;
    /**
     * 仅插入的失败处理参数:
     */
    private String insertErrorStrategy;
    /**
     * 任务是否建表
     */
    private Boolean createAlterTable;
    /**
     * hive存储类型
     */
    private HiveStorageTypeEnum hiveStorageType;
    /**
     * 空值处理
     */
    private String emptyDataStrategy;
    /**
     * 自动提交事务
     */
    private String autoCommit;
    /**
     * null，none、upper、lower
     */
    private String fieldUppLow;

    // 表的主键模式--1——唯一，2-重复
    String tablePKModel = PrimaryKeyModelEnum.UNIQUE.getName();

}
