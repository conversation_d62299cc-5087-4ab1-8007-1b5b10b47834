package com.joyadata.engine.common.beans.dto.sink;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/12/18
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class EngineSinkAdbGpdistDTO implements Serializable {

    /**
     * 数据源id
     */
    private String datasourceInfoId;

    /**
     * 文件临时储存路径
     */
    private String path;

    private String fileFormatType = "csv";

    /**
     * 外表文件列分隔符
     */
    private String fieldDelimiter = ",";

    private String adbUrl;

    private String adbDriver;

    private String adbUser;

    private String adbPassword;

    /**
     * 模式
     */
    private String adbDatabase;

    private String schemaName;

    /**
     * 表名称
     */
    private String adbTable;

    /**
     * gpfdist地址
     */
    private String adbGpfdistDddress;

    /**
     * 外表文件路径
     */
    private String adbTmpFilePath;

    /**
     * gpfdist路径
     */
    private String adbGpfdistPath;

    /**
     * 外表名
     */
    private String adbExternalTableName;

    /**
     * 外表schema
     */
    private String adbExternalTableSchema;
}
