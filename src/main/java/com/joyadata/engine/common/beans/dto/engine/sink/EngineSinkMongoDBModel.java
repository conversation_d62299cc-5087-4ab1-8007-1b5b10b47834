package com.joyadata.engine.common.beans.dto.engine.sink;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/2/21
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class EngineSinkMongoDBModel implements Serializable {
    /**
     * 当source_table_name未指定时，当前插件处理配置文件中前一个插件输出的dataset数据集。当指定source_table_name时，当前插件处理的是对应此参数的数据集。
     */
    private String sourceTableName;
    /**
     * mongodb://192.168.0.100:27017/mydb、mongodb://192.168.0.1:27017,192.168.0.2:27017,192.168.0.3:27017/mydb?replicaSet=xxx
     */
    private String uri;
    /**
     * username
     */
    private String database;
    /**
     * 集合，对应表名
     */
    private String collection;

    /**
     * MongoDB's BSON and seatunnel data structure mapping
     * 数据结构：schema = {
     * fields {
     * c_map = "map<string, string>"
     * c_array = "array<int>"
     * c_string = string
     * c_boolean = boolean
     * c_int = int
     * c_bigint = bigint
     * c_double = double
     * c_bytes = bytes
     * c_date = date
     * c_decimal = "decimal(38, 18)"
     * c_timestamp = timestamp
     * c_row = {
     * c_map = "map<string, string>"
     * c_array = "array<int>"
     * c_string = string
     * c_boolean = boolean
     * c_int = int
     * c_bigint = bigint
     * c_double = double
     * c_bytes = bytes
     * c_date = date
     * c_decimal = "decimal(38, 18)"
     * c_timestamp = timestamp
     * }
     * }
     * }
     */
    private LinkedHashMap<String, String> schema;

    /**
     * Specifies the maximum number of buffered rows per batch request. 默认值：1000
     */
    private String bufferFlushMaxRows;

    /**
     * Specifies the maximum interval of buffered rows per batch request, the unit is millisecond. 默认值：30000
     */
    private String bufferFlushInterval;

    /**
     * Specifies the max number of retry if writing records to database failed. 默认值：3
     */
    private String retryMax;

    /**
     * Specifies the retry time interval if writing records to database failed, the unit is millisecond. 默认值：1000
     */
    private String retryInterval;

    /**
     * Whether to write documents via upsert mode. 默认值：false
     */
    private String upsertEnable;

    /**
     * The primary keys for upsert/update. Keys are in `["id","name",...]` format for properties.
     */
    private List primaryKey;

    /**
     * Whether to use transactions in MongoSink (requires MongoDB 4.2+). 默认值：false
     */
    private String transaction;
}
