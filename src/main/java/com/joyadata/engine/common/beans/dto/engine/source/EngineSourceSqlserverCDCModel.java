package com.joyadata.engine.common.beans.dto.engine.source;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/2/21
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class EngineSourceSqlserverCDCModel implements Serializable {
    /**
     * 结果表名
     */
    private String resultTableName;

    /**
     * SQL Server 用户名
     */
    private String username;

    /**
     * SQL Server 密码
     */
    private String password;

    /**
     * SQL Server 数据库名称列表，多个用逗号分割。
     */
    private String databaseNames;

    /**
     * SQL Server 表名称列表，多个用逗号分割。
     */
    private String tableNames;

    /**
     * JDBC连接URL
     */
    private String baseUrl;


}
