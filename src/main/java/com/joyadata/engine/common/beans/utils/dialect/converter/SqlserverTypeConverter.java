package com.joyadata.engine.common.beans.utils.dialect.converter;

import com.joyadata.engine.common.beans.constants.Constants;
import com.joyadata.engine.common.beans.dto.DdlDTO;
import com.joyadata.engine.common.beans.dto.datasource.MetadataColumnDTO;
import com.joyadata.engine.common.beans.utils.dialect.AbstractDatabaseTypeConverter;
import com.joyadata.engine.common.beans.utils.dialect.paramters.DDLParameters;
import groovy.lang.Tuple2;
import groovy.lang.Tuple3;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.sql.Types;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2025/4/27 17:50
 */
@Slf4j
public class SqlserverTypeConverter extends AbstractDatabaseTypeConverter {
    @Override
    protected Tuple3<String, String, String> doGenerateDDL(DDLParameters ddlParameters) {
        String schema = ddlParameters.getSchema();
        String tableName = ddlParameters.getTableName();
        List<DdlDTO> columns = ddlParameters.getColumns();
        String tableCnName = ddlParameters.getTableCnName();
        String fieldUppLower = ddlParameters.getFieldUppLower();
        if (StringUtils.isNotBlank(fieldUppLower)) {
            tableName = getUppOrLowerCase(fieldUppLower, tableName);
        }
        String checkTableSql = "select 1 from " + tableName + " where 1=2";
        String create = "create table " + tableName + " (";
        if (StringUtils.isNotBlank(schema)) {
            if (StringUtils.isNotBlank(fieldUppLower)) {
                schema = getUppOrLowerCase(fieldUppLower, schema);
            }
            create = "create table " + schema + "." + tableName + " (";
            checkTableSql = "select 1 from " + schema + "." + tableName + " where 1=2";
        }
        StringBuilder ddl = new StringBuilder(create);
        columns.forEach(ddlDTO -> {
            int dataType = ddlDTO.getDataTypeJava();// Integer.valueOf(column.get("data_type"));
            int columnSize = ddlDTO.getColumnSize();// Integer.valueOf(column.get("max_length"));
            int decimalDigits = ddlDTO.getDecimalDigits(); //Integer.valueOf(column.get("decimal_digits"));
            String dataName = ddlDTO.getDataType(); //column.get("type_name");
            //String columnName = ddlDTO.getColumnName().toLowerCase(); //column.get("column_name").toLowerCase();
            String columnName;
            if (StringUtils.isNotBlank(fieldUppLower)) {
                columnName = getUppOrLowerCase(fieldUppLower, ddlDTO.getColumnName());
            } else {
                columnName = ddlDTO.getColumnName().toLowerCase(); //column.get("column_name").toLowerCase();
            }
            //String cnName = ddlDTO.getColumnCnName();// StringUtils.defaultString(column.get("remarks"),"");
            //int position = ddlDTO.getPosition();
            String dataTypeString = getSqlServerTypeFromSqlType(dataType, dataName, columnSize, decimalDigits);
            //log.info("当前字段名称是 {} ,字段javaType是 {} ,元数据名称是 {},转换为 {}", columnName, dataType, dataName, dataTypeString);
            ddl.append("\"").append(columnName).append("\" ").append(dataTypeString).append(",");
            //list.add(new DdlDTO(columnName,cnName,dataName,dataType,columnSize,decimalDigits,position));
        });
        String result = ddl.toString();
        result = result.substring(0, result.length() - 1);
        result = result + ")";
        log.info("SqlServerFromMetadata生成SqlServer建表语句是 {}", result);
        log.info("SqlServerFromMetadata生成SqlServer检测表是否存在的语句是 {}", checkTableSql);
        Tuple3<String, String, String> tuple3 = new Tuple3<>(result, "", checkTableSql);
        return tuple3;
    }

    public static String getSqlServerTypeFromSqlType(int dataType, String dataName, int columnSize, int decimalDigits) {
        switch (dataType) {
            case Types.INTEGER:
            case Types.SMALLINT:
            case Types.TINYINT:
                return "INT";
            case Types.BIGINT:
                return "BIGINT";
            case Types.FLOAT:
                return "REAL";
            case Types.DOUBLE:
                return "FLOAT";
            case Types.DECIMAL:
            case Types.NUMERIC:
                if ((columnSize == 0 && decimalDigits == -127) || columnSize >= 38) {
                    return "DECIMAL(38,0)";
                } else {
                    return "DECIMAL(" + columnSize + "," + decimalDigits + ")";
                }
            case Types.DATE:
                return "DATE";
            case Types.TIME:
                return "TIME";
            case Types.TIMESTAMP:
                return "DATETIME";
            case Types.BINARY:
            case Types.VARBINARY:
            case Types.LONGVARBINARY:
                return "VARCHAR(MAX)";
            case Types.CHAR:
            case Types.VARCHAR:
            case Types.NVARCHAR:
                if (columnSize > 4000 || columnSize == 0) {
                    return "NVARCHAR(MAX)";
                } else {
                    return "NVARCHAR(" + columnSize + ")";
                }
            case Types.LONGVARCHAR:
                return "VARCHAR(MAX)";
            case Types.CLOB:
            case Types.NCLOB:
            case Types.BLOB:
                return "VARCHAR(MAX)";
            default:
                if (StringUtils.isNotBlank(dataName)) {
                    if ("NVARCHAR2".equalsIgnoreCase(dataName)) {
                        if (columnSize > 4000 || columnSize == 0) {
                            return "VARCHAR(MAX)";
                        } else {
                            return "NVARCHAR(" + columnSize + ")";
                        }
                    } else if ("VARCHAR2".equalsIgnoreCase(dataName)) {
                        if (columnSize > 4000 || columnSize == 0) {
                            return "VARCHAR(MAX)";
                        } else {
                            return "NVARCHAR(" + columnSize + ")";
                        }
                    } else if (dataName.startsWith("TIMESTAMP")) {
                        return "DATETIME";
                    } else if ("NCHAR".equalsIgnoreCase(dataName)) {
                        return "NVARCHAR(" + columnSize + ")";
                    } else if ("BINARY_FLOAT".equalsIgnoreCase(dataName) || "BINARY_DOUBLE".equalsIgnoreCase(dataName)
                            || "BFILE".equalsIgnoreCase(dataName) || "ROWID".equalsIgnoreCase(dataName)
                            || "UROWID".equalsIgnoreCase(dataName)) {
                        return "VARCHAR(MAX)";
                    } else if ("INTERVAL YEAR(2) TO MONTH".equalsIgnoreCase(dataName)) {
                        return "VARCHAR(10)";
                    } else if (dataName.startsWith("TIMESTAMP")) {
                        return "DATETIME";
                    } else if (dataName.equalsIgnoreCase("serial")) {//pg数据库转int
                        return "INT";
                    }
                }
                return "VARCHAR(4000)";
        }
    }

    @Override
    public Tuple2<String, List<String>> generateAddPkSql(String schema, String tableName, List<MetadataColumnDTO> pkFieldList, String fieldUppLower) {
        if (StringUtils.isNotBlank(fieldUppLower)) {
            tableName = getUppOrLowerCase(fieldUppLower, tableName);
            pkFieldList.forEach(t -> {
                t.setColumnName(getUppOrLowerCase(Constants.UPPER, t.getColumnName()));
            });
        }
        if (StringUtils.isNotBlank(schema)) {
            if (StringUtils.isNotBlank(fieldUppLower)) {
                schema = getUppOrLowerCase(fieldUppLower, schema);
            }
        }
        String finalTableName = tableName;
        String finalSchema = schema;
        String pkField = pkFieldList.stream().map(c -> c.getColumnName().toLowerCase()).collect(Collectors.joining(","));
        String pkSql = "ALTER  TABLE  " + tableName + " ADD PRIMARY KEY (" + pkField + ")";
        //sqlserver添加主键时要求字段有字段不能为空的条件
        List<String> notNullSqlList = new ArrayList<>();
        pkFieldList.forEach(columnFilterDTO -> {
            String dataTypeString = getSqlServerTypeFromSqlType(Integer.valueOf(columnFilterDTO.getDataType()), columnFilterDTO.getDataType(),
                    Integer.parseInt(columnFilterDTO.getColumnLength()), Integer.parseInt(columnFilterDTO.getColumnScale()));
            String notNullSql = "ALTER TABLE " + finalTableName + " ALTER COLUMN " + columnFilterDTO.getColumnName().toLowerCase() + " " + dataTypeString + " NOT NULL";
            if (StringUtils.isNotBlank(finalSchema)) {
                notNullSql = "ALTER TABLE " + finalSchema + "." + finalTableName + " ALTER COLUMN " + columnFilterDTO.getColumnName().toLowerCase() + " " + dataTypeString + " NOT NULL";
            }
            notNullSqlList.add(notNullSql);
        });
        if (StringUtils.isNotBlank(finalSchema)) {
            pkSql = "ALTER  TABLE " + finalSchema + "." + tableName + " ADD PRIMARY KEY (" + pkField + ")";
        }
        Tuple2<String, List<String>> tuple2 = new Tuple2<>(pkSql, notNullSqlList);
        return tuple2;
    }
}
