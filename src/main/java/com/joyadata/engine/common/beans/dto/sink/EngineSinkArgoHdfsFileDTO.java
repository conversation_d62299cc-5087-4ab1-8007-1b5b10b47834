package com.joyadata.engine.common.beans.dto.sink;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/7/9 16:10
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class EngineSinkArgoHdfsFileDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 数据源id
     */
    private String datasourceInfoId;

    private String defaultFS;

    /**
     * 外表文件路径(hdfs路径)
     */
    private String path;

    /**
     * 文件类型
     */
    private String fileFormatType = "text";
    /**
     * 外表文件列分隔符
     * Only used when file_format_type is text
     */
    private String fieldDelimiter = ",";
    /**
     * 外表文件行分隔符
     * Only used when file_format_type is text
     */
    private String rowDelimiter = "\\n";

    private String fileNameExpression;

    private String argoUrl;
    private String argoUser;
    private String argoPassword;
    private String argoSchema;
    /**
     * 表名称
     */
    private String argoTable;
    /**
     * 外表名称
     */
    private String argoTmpTableName;
    private String krb5Path;
    private String kerberosPrincipal;
    private String kerberosKeytabPath;
    private String hdfsSitePath;
    /**
     * 空值处理
     */
    private String emptyDataStrategy;
    /**
     * 外表schema
     */
    private String argoTmpSchema;
}