package com.joyadata.engine.common.beans.dto.source;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class EngineSourceOssAliFile2FileDTO {

    /**
     * 数据源id
     */
    private String datasourceInfoId;
    /**
     * 源文件路径
     */
    private String path;
    /**
     * 是否文件同步
     * 文件同步:true
     * 文件夹同步:false
     */
    private Boolean isFile;
}
