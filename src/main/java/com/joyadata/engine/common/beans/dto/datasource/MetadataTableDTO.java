package com.joyadata.engine.common.beans.dto.datasource;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version V1.0
 * @ClassName: MetadataTableDTO
 * @date 2024/2/22
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class MetadataTableDTO {
    /**
     * 主键
     */
    private String id;

    /**
     * 表名
     */
    private String tableName;

    /*
     * 库名
     */
    private String databaseName;

    /**
     * 表中文名称
     */
    private String tableCnName;

    /**
     * 表别名
     */
    private String tableAlias;

    /**
     * 表类型
     */
    private String tableType;

    /**
     * 表注释
     */
    private String tableComment;

    /**
     * 数据源id
     */
    private String datasourceInfoId;

    /**
     * 数据源类型
     */
    private String datasourceType;

   
}
