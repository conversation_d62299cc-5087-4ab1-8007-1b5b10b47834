package com.joyadata.engine.common.beans.dto.engine.source;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class EngineSourceLocalFile2FileModel {
    /**
     * 文件路径
     */
    private String path;

    private String fileFormatType = "binary";
    /**
     * 结果表名称
     */
    private String resultTableName;
}
