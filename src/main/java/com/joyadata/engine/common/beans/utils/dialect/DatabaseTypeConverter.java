package com.joyadata.engine.common.beans.utils.dialect;

import com.joyadata.engine.common.beans.dto.datasource.MetadataColumnDTO;
import com.joyadata.engine.common.beans.utils.dialect.paramters.DDLParameters;
import groovy.lang.Tuple2;
import groovy.lang.Tuple3;

import java.util.List;

public interface DatabaseTypeConverter {
    Tuple3<String, String, String> generateDDL(DDLParameters ddlParameters);
    Tuple2<String, List<String>> generateAddPkSql(String schema, String tableName, List<MetadataColumnDTO> pkFieldList, String fieldUppLower);
}