package com.joyadata.engine.common.beans.dto.engine.sink;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version V1.0
 * @ClassName: EngineSinkLocalFileModel
 * @Description: TODO
 * @date 2024/4/16
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class EngineSinkLocalFileModel implements Serializable {
    /**
     * 文件名称
     */
    private String finalName;
    /**
     * 文件写入路径
     */
    private String path;
    /**
     * 结果文件将首先写入tmp路径，然后使用mv将tmp目录提交到目标目录。
     */
    private String tmpPath;
    /**
     * 是否需要自定义文件名
     */
    private String customFilename;
    /**
     * 仅当custom_filename为true时使用;默认"${transactionId}"
     */
    private String fileNameExpression;
    /**
     * 仅当custom_filename为true时使用;默认"yyyy.MM.dd"
     */
    private String filenameTimeFormat;
    /**
     * 文件类型:text json csv orc parquet excel
     */
    private String fileFormatType;
    /**
     * 数据行中列之间的分隔符。仅当file_format_type为text时使用;
     */
    private String fieldDelimiter;
    /**
     * 文件中行之间的分隔符，仅当file_format_type为text时使用
     */
    private String rowDelimiter;
    /**
     * 是否需要处理分区。true / false
     */
    private String havePartition;
    /**
     * 只有在have_partition为true时才使用，根据所选字段对数据进行分区。
     */
    private String partitionBy;
    /**
     * 仅当have_partition为true时使用。
     * 如果指定了partition_by，我们将根据分区信息生成相应的分区目录，最终文件将放置在分区目录中。
     * 默认partition_dir_expression为$｛k0｝=$｛v0｝/$｛k1｝=${v1｝//$｛kn｝=$｛vn｝/。k0是第一个分区字段，v0是第一分区字段的值。
     */
    private String partitionDirExpression;
    /**
     * 仅当have_partition为true时使用。
     * 如果is_partition_file_write_in_file为true，则分区字段及其值将写入数据文件。
     * 例如，如果要编写配置单元数据文件，其值应为false。
     */
    private String isPartitionFieldWriteInFile;
    /**
     * 哪些列需要写入文件，默认值是从Transform或Source获取的所有列。字段的顺序决定了文件实际写入的顺序。
     * 当此参数为空时，所有字段都是汇点列
     */
    private String sinkColumns;
    /**
     * 文件最大行数;默认1000000
     */
    private String batchSize;
    /**
     * 文件的压缩编解码器和支持的详细信息如下所示：
     * txt:lzo无
     * json:lzo无
     * csv:lzo无
     * 兽人：lzo快lz4 zlib无
     * 镶木地板：lzo snappy lz4 gzip brotli zstd none
     * 提示：excel类型不支持任何压缩格式
     */
    private String compressCodec;
    /**
     * 仅当file_format_type为excel时使用。当文件格式为Excel时，可以缓存在内存中的最大数据项数。
     */
    private String maxRowsInMemory;
    /**
     * 仅当file_format_type为excel时使用
     * 编写工作簿的工作表
     */
    private String sheetName;
    /**
     * 仅当file_format_type为text、csv时使用。
     * false：不写头，true：写头
     */
    private String enableHeaderWrite;
    /**
     * 当source_table_name未指定时，当前插件处理配置文件中前一个插件输出的dataset数据集。当指定source_table_name时，当前插件处理的是对应此参数的数据集。
     */
    private String sourceTableName;

    /**
     * 多次执行是否覆盖文件，默认是不覆盖。
     */
    private boolean overwriteFile = false;
    /**
     * 校验文件
     */
    private String validateFile;
    /**
     * 校验内容
     */
    private String validateContent;
    /**
     * 空值处理
     */
    private String emptyDataStrategy;

    /**
     * YYYY_MM_DD_HH_MM_SS("yyyy-MM-dd HH:mm:ss"),
     * YYYY_MM_DD_HH_MM_SS_SSSSSS("yyyy-MM-dd HH:mm:ss.SSSSSS"),
     * YYYY_MM_DD_HH_MM_SS_SPOT("yyyy.MM.dd HH:mm:ss"),
     * YYYY_MM_DD_HH_MM_SS_SLASH("yyyy/MM/dd HH:mm:ss"),
     * YYYY_MM_DD_HH_MM_SS_NO_SPLIT("yyyyMMddHHmmss"),
     * YYYY_MM_DD_HH_MM_SS_ISO8601("yyyy-MM-dd'T'HH:mm:ss"),
     * YYYY_MM_DD_HH_MM_SS_SSS_ISO8601("yyyy-MM-dd'T'HH:mm:ss.SSS"),
     * YYYY_MM_DD_HH_MM_SS_SSSSSS_ISO8601("yyyy-MM-dd'T'HH:mm:ss.SSSSSS"),
     * YYYY_MM_DD_HH_MM_SS_SSSSSSSSS_ISO8601("yyyy-MM-dd'T'HH:mm:ss.SSSSSSSSS");
     */
    private String datetimeFormat = "yyyy-MM-dd HH:mm:ss";

    /**
     * YYYY_MM_DD("yyyy-MM-dd"),
     * YY_MM_DD("yy-MM-dd"),
     * YYYY_MM_DD_SPOT("yyyy.MM.dd"),
     * YYYY_MM_DD_SLASH("yyyy/MM/dd");
     */
    private String dateFormat = "yyyy-MM-dd";
    /**
     * HH_MM_SS("HH:mm:ss"),
     * HH_MM_SS_SSS("HH:mm:ss.SSS");
     */
    private String timeFormat = "HH:mm:ss";
    /**
     * 是否定长处理
     * true：字段内容小于设计长度要用空格补齐
     * false: 保持原样
     */
    private String fixedFieldLengthStrategy = "false";
    /**
     * 校验文件是否根据真实数据文件生成多个
     * true：生成多个
     * false: 生成单个
     */
    private String validates = "false";
    private boolean cleanTargetFolder = false;
    private String encoding;
    /**
     * null值处理
     */
    private String nullToValue;
    /**
     * 浙江农信需求，分区抽取tdsql数据落到文件，文件名中需要有唯一标识否则多读多写会覆盖文件，将每一组读写的分区号传去st，放进文件名中
     */
    private String tdsqlPartitionName;
}
