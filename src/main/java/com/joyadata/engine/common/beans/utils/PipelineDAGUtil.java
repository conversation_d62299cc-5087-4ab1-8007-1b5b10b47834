package com.joyadata.engine.common.beans.utils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import groovy.lang.Tuple2;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/4/15
 */
public class PipelineDAGUtil {

    public static Map<Integer, Tuple2<String, String>> createDagPipeline(JSONObject obj) {
        JSONObject rawScript = obj.getJSONObject("rawScript");
        JSONArray source = rawScript.getJSONArray("source");
        Map<Integer, Tuple2<String, String>> dags = new HashMap<>();
        for (int i = 0; i < source.size(); i++) {
            JSONObject jsonObject = source.getJSONObject(i);
            String sourceTableName = jsonObject.getString("resultTableName");
            String sinkTableName = findSinkTableName(rawScript, sourceTableName);
            Tuple2<String, String> tuple2 = new Tuple2<>(sourceTableName, sinkTableName);
            dags.put((i + 1), tuple2);
        }
        return dags;
    }

    private static String findSinkTableName(JSONObject rawScript, String sourceTableName) {
        String sinkTableName = null;
        JSONArray transform = rawScript.getJSONArray("transforms");
        for (int i = 0; i < transform.size(); i++) {
            JSONObject obj = transform.getJSONObject(i);
            if (obj.getString("sourceTableName").equalsIgnoreCase(sourceTableName)) {
                sinkTableName = obj.getString("resultTableName");
                break;
            }
        }
        JSONArray sink = rawScript.getJSONArray("sink");
        for (int i = 0; i < sink.size(); i++) {
            JSONObject obj = sink.getJSONObject(i);
            if (null == sinkTableName && obj.getString("sourceTableName").equalsIgnoreCase(sourceTableName)) {
                sinkTableName = obj.getString("sourceTableName");
                break;
            } else {
                if (obj.getString("sourceTableName").equalsIgnoreCase(sinkTableName)) {
                    sinkTableName = obj.getString("sourceTableName");
                    break;
                }
            }

        }
        return sinkTableName;
    }
}
