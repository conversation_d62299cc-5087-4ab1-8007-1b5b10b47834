package com.joyadata.engine.common.beans.dto.source;

import com.joyadata.engine.common.beans.dto.RowFilterDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.LinkedHashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/7/9 13:44
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class EngineSourceAdbGpdistDTO extends EngineBaseSourceDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 文件临时存储路径
     */
    private String path;


    private String fileFormatType = "csv";

    /**
     * 仅当file_format_type为文本时使用
     */
    private String fieldDelimiter = ",";

    private String adbUrl;

    private String adbDriver;

    private String adbUser;

    private String adbPassword;

    private String adbDatabase;

    private String adbTable;

    private LinkedHashMap<String, String> adbSchema;

    private String schemaName;
    private String adbGpfdistDddress;

    private String adbPrefix;

    /**
     * 外表文件路径
     */
    private String adbTmpFilePath;

    /**
     * 外表名称
     */
    private String adbExternalTableName;

    /**
     * gpfdist路径
     */
    private String adbGpfdistPath;

    /**
     * 外表文件列分隔符
     */
    private String adbExternalTableDelimiter;
    /**
     * 外表schema
     */
    private String adbExternalTableSchema;

    /**
     * where条件
     */
    private List<RowFilterDTO> whereCondition;

    /**
     * 自定义SQL条件
     */
    private String customWhereSql;
}