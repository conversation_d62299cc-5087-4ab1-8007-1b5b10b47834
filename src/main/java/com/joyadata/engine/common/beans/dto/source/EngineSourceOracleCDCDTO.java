package com.joyadata.engine.common.beans.dto.source;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/3/2
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class EngineSourceOracleCDCDTO implements Serializable {

    /**
     * 数据源id
     */
    private String datasourceInfoId;

    /**
     * 表id
     */
    private String tableId;


    /**
     * 表名，多个用逗号分割
     */
    //private String tableNames;

    /**
     * 关闭超时时间
     */
    private String sourceReaderCloseTimeout;
    /**
     * CDC源的可选启动模式，有效的枚举是“initial”，“earliest”，“latest”，“timestamp”或“specific”
     */
    private String startupMode;

    /**
     * Debezium嵌入式引擎 配置
     * 参考：     debezium {
     * log.mining.strategy = "online_catalog"
     * log.mining.continuous.mine = "true"
     * database.oracle.jdbc.timezoneAsRegion = "false"
     * }
     */
    private Map<String, String> debezium;

}
