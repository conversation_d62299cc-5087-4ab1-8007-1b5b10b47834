package com.joyadata.engine.common.beans.dto.engine;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/2/21
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class EngineEnvMode implements Serializable {
    /**
     * 批模式BATCH、流模式 STREAMING。默认批模式
     */
    private String jobModel = "BATCH";
    /**
     * 执行任务并行度
     */
    private String executionParallelism = "10";

}
