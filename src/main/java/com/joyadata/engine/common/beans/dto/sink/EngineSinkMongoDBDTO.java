package com.joyadata.engine.common.beans.dto.sink;

import com.joyadata.engine.common.beans.dto.datasource.MetadataColumnDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.Duration;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/3/1
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class EngineSinkMongoDBDTO extends EngineBaseSinkDTO implements Serializable {

    /**
     * 写入表名称
     */
    private String tableName;

    /**
     * 表结构信息
     */
    private LinkedHashMap<String, String> schema;

    /**
     * Specifies the maximum number of buffered rows per batch request. 默认值：1000
     */
    private String bufferFlushMaxRows;

    /**
     * Specifies the maximum interval of buffered rows per batch request, the unit is millisecond. 默认值：30000
     */
    private String bufferFlushInterval;

    /**
     * Specifies the max number of retry if writing records to database failed. 默认值：3
     */
    private String retryMax;

    /**
     * Specifies the retry time interval if writing records to database failed, the unit is millisecond. 默认值：1000
     */
    private String retryInterval;

    /**
     * Whether to write documents via upsert mode. 默认值：false
     */
    private String upsertEnable;

    /**
     * The primary keys for upsert/update. Keys are in `["id","name",...]` format for properties.
     */
    private List primaryKey;

    /**
     * Whether to use transactions in MongoSink (requires MongoDB 4.2+). 默认值：false
     */
    private String transaction;

    /**
     * 传输列
     */
    private List<MetadataColumnDTO> fieldList;
    /**
     * 库名
     */
    private String dbName;
    /**
     * 模式名
     */
    private String schemaName;
}
