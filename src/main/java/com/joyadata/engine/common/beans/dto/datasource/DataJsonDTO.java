package com.joyadata.engine.common.beans.dto.datasource;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version V1.0
 * @ClassName: DataJsonDTO
 * @date 2024/2/22
 * @desc 解密数据源后的datajson
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class DataJsonDTO {
    /**
     * 用户名
     */
    private String username;
    /**
     * 密码
     */
    private String password;
    /**
     * jdbc连接串
     */
    private String jdbcUrl;
    /**
     * 数据源类型
     */
    private String dataType;
    /**
     * 模式名
     */
    private String schema;

    private String driverClassName;

    /**
     * Hive元数据库数据源id
     */
    private String datasourceMetadata;
}
