package com.joyadata.engine.common.beans.dto.engine;


import com.joyadata.engine.common.beans.dto.engine.sink.EngineHttpBaseSinkModel;
import com.joyadata.engine.common.beans.dto.engine.sink.EngineMaxComputeBaseSinkModel;
import com.joyadata.engine.common.beans.dto.engine.sink.EngineSinkAdbGpdistModel;
import com.joyadata.engine.common.beans.dto.engine.sink.EngineSinkArgoHdfsFileModel;
import com.joyadata.engine.common.beans.dto.engine.sink.EngineSinkClickhouseModel;
import com.joyadata.engine.common.beans.dto.engine.sink.EngineSinkConsoleHoleModel;
import com.joyadata.engine.common.beans.dto.engine.sink.EngineSinkDWSPGModel;
import com.joyadata.engine.common.beans.dto.engine.sink.EngineSinkDataHubModel;
import com.joyadata.engine.common.beans.dto.engine.sink.EngineSinkDorisModel;
import com.joyadata.engine.common.beans.dto.engine.sink.EngineSinkElasticsearchModel;
import com.joyadata.engine.common.beans.dto.engine.sink.EngineSinkFTPModel;
import com.joyadata.engine.common.beans.dto.engine.sink.EngineSinkHbaseModel;
import com.joyadata.engine.common.beans.dto.engine.sink.EngineSinkHdfsFileModel;
import com.joyadata.engine.common.beans.dto.engine.sink.EngineSinkHiveModel;
import com.joyadata.engine.common.beans.dto.engine.sink.EngineSinkInspurOSSFile2FileModel;
import com.joyadata.engine.common.beans.dto.engine.sink.EngineSinkJDBCModel;
import com.joyadata.engine.common.beans.dto.engine.sink.EngineSinkKafkaModel;
import com.joyadata.engine.common.beans.dto.engine.sink.EngineSinkKuduModel;
import com.joyadata.engine.common.beans.dto.engine.sink.EngineSinkLocalFile2FileModel;
import com.joyadata.engine.common.beans.dto.engine.sink.EngineSinkLocalFileModel;
import com.joyadata.engine.common.beans.dto.engine.sink.EngineSinkMongoDBModel;
import com.joyadata.engine.common.beans.dto.engine.sink.EngineSinkOssAliFile2FileModel;
import com.joyadata.engine.common.beans.dto.engine.sink.EngineSinkOssAliFileModel;
import com.joyadata.engine.common.beans.dto.engine.sink.EngineSinkOssHuaweiFileModel;
import com.joyadata.engine.common.beans.dto.engine.sink.EngineSinkS3File2FileModel;
import com.joyadata.engine.common.beans.dto.engine.sink.EngineSinkS3FileModel;
import com.joyadata.engine.common.beans.dto.engine.sink.EngineSinkSFTP2FileModel;
import com.joyadata.engine.common.beans.dto.engine.sink.EngineSinkSFTPModel;
import com.joyadata.engine.common.beans.dto.engine.source.EngineHttpBaseSourceModel;
import com.joyadata.engine.common.beans.dto.engine.source.EngineMaxComputeBaseSourceModel;
import com.joyadata.engine.common.beans.dto.engine.source.EngineSourceAdbGpdistModel;
import com.joyadata.engine.common.beans.dto.engine.source.EngineSourceArgoHdfsFileModel;
import com.joyadata.engine.common.beans.dto.engine.source.EngineSourceClickhouseModel;
import com.joyadata.engine.common.beans.dto.engine.source.EngineSourceDataHubModel;
import com.joyadata.engine.common.beans.dto.engine.source.EngineSourceDorisModel;
import com.joyadata.engine.common.beans.dto.engine.source.EngineSourceElasticsearchModel;
import com.joyadata.engine.common.beans.dto.engine.source.EngineSourceFTPModel;
import com.joyadata.engine.common.beans.dto.engine.source.EngineSourceHbaseModel;
import com.joyadata.engine.common.beans.dto.engine.source.EngineSourceHdfsFileModel;
import com.joyadata.engine.common.beans.dto.engine.source.EngineSourceInspurOSSFile2FileModel;
import com.joyadata.engine.common.beans.dto.engine.source.EngineSourceJdbcModel;
import com.joyadata.engine.common.beans.dto.engine.source.EngineSourceKafkaModel;
import com.joyadata.engine.common.beans.dto.engine.source.EngineSourceKuduModel;
import com.joyadata.engine.common.beans.dto.engine.source.EngineSourceLocalFile2FileModel;
import com.joyadata.engine.common.beans.dto.engine.source.EngineSourceLocalFileModel;
import com.joyadata.engine.common.beans.dto.engine.source.EngineSourceMongoDBModel;
import com.joyadata.engine.common.beans.dto.engine.source.EngineSourceMysqlCDCModel;
import com.joyadata.engine.common.beans.dto.engine.source.EngineSourceOracleCDCModel;
import com.joyadata.engine.common.beans.dto.engine.source.EngineSourceOssAliFile2FileModel;
import com.joyadata.engine.common.beans.dto.engine.source.EngineSourceOssAliFileModel;
import com.joyadata.engine.common.beans.dto.engine.source.EngineSourceOssHuaweiFileModel;
import com.joyadata.engine.common.beans.dto.engine.source.EngineSourcePostgresCDCModel;
import com.joyadata.engine.common.beans.dto.engine.source.EngineSourceS3File2FileModel;
import com.joyadata.engine.common.beans.dto.engine.source.EngineSourceS3FileModel;
import com.joyadata.engine.common.beans.dto.engine.source.EngineSourceSFTP2FileModel;
import com.joyadata.engine.common.beans.dto.engine.source.EngineSourceSFTPModel;
import com.joyadata.engine.common.beans.dto.engine.source.EngineSourceSqlserverCDCModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/2/21
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class EngineModel implements Serializable {
    /**
     * 环境信息配置
     */
    private EngineEnvMode env;
    /**
     * 源端配置
     */
    private List<EngineSourceJdbcModel> jdbcSources = new ArrayList<>();

    /**
     * 源端 Oracle 配置
     */
    private List<EngineSourceOracleCDCModel> oracleSources = new ArrayList<>();
    /**
     * 源端 Mysql 配置
     */
    private List<EngineSourceMysqlCDCModel> mysqlSources = new ArrayList<>();
    /**
     * 源端sqlserver配置
     */
    private List<EngineSourceSqlserverCDCModel> sqlserverSources = new ArrayList<>();

    /**
     * 源端postgres配置
     */
    private List<EngineSourcePostgresCDCModel> postgresSources = new ArrayList<>();


    /**
     * 源端elasticsearch配置
     */
    private List<EngineSourceElasticsearchModel> elasticsearchSources = new ArrayList<>();

    /**
     * 源端mongodb配置
     */
    private List<EngineSourceMongoDBModel> mongodbSources = new ArrayList<>();

    /**
     * 源端kudu配置
     */
    private List<EngineSourceKuduModel> kuduSources = new ArrayList<>();

    /**
     * 源端hdfsfile配置
     */
    private List<EngineSourceHdfsFileModel> hdfsFileSources = new ArrayList<>();

    /**
     * 源端localfile配置
     */
    private List<EngineSourceLocalFileModel> localFileSources = new ArrayList<>();

    /**
     * 源端oss_ali配置
     */
    private List<EngineSourceOssAliFileModel> ossAliSources = new ArrayList<>();

    /**
     * 源端kafka配置
     */
    private List<EngineSourceKafkaModel> kafkaSources = new ArrayList<>();
    /**
     * 源端s3配置
     */
    private List<EngineSourceS3FileModel> s3FileSources = new ArrayList<>();

    /**
     * doris配置
     */
    private List<EngineSourceDorisModel> dorisSources = new ArrayList<>();

    /**
     * 源端华为oss配置
     */
    private List<EngineSourceOssHuaweiFileModel> ossHuaweiSources = new ArrayList<>();

    /**
     * 源端hbase配置
     */
    private List<EngineSourceHbaseModel> hbaseSources = new ArrayList<>();

    /**
     * 源端localfile2file配置
     */
    private List<EngineSourceLocalFile2FileModel> localFile2FileSources = new ArrayList<>();

    /**
     * 源端OssAliFile2File配置
     */
    private List<EngineSourceOssAliFile2FileModel> ossAliFile2FileSources = new ArrayList<>();

    /**
     * 源端s3File2File配置
     */
    private List<EngineSourceS3File2FileModel> s3File2FileSources = new ArrayList<>();

    /**
     * 源端sftpFile2File配置
     */
    private List<EngineSourceSFTP2FileModel> sftpFile2FileSources = new ArrayList<>();

    /**
     * 源端inspurOSSFile2File配置
     */
    private List<EngineSourceInspurOSSFile2FileModel> inspurOSSFile2FileSources = new ArrayList<>();

    /**
     * 目标端 http source组件配置
     */

    private List<EngineHttpBaseSourceModel> httpBaseSources = new ArrayList<>();

    /**
     * 目标端 maxCompute source组件配置
     */

    private List<EngineMaxComputeBaseSourceModel> maxComputeSources = new ArrayList<>();

    /**
     * 转换ETL中间过程配置
     */
    private List<EngineTransformModel> transforms = new ArrayList<>();

    /**
     * 源端sftp配置
     */
    private List<EngineSourceSFTPModel> sftpSources = new ArrayList<>();

    /**
     * 源端ftp配置
     */
    private List<EngineSourceFTPModel> ftpSources = new ArrayList<>();
    /**
     * 源端dataHub配置
     */
    private List<EngineSourceDataHubModel> dataHubSources = new ArrayList<>();
    /**
     * 源端clickhouse 配置
     */
    private List<EngineSourceClickhouseModel> clickhouseSources = new ArrayList<>();

    /**
     * 源端argo localFile配置
     */
    private List<EngineSourceAdbGpdistModel> adbGpdistSources = new ArrayList<>();

    /**
     * 源端argo hdfsFile配置
     */
    private List<EngineSourceArgoHdfsFileModel> argoHdfsFileSources = new ArrayList<>();


    /**
     * 目标端 JDBC 配置
     */
    private List<EngineSinkJDBCModel> jdbcSinks = new ArrayList<>();
    /**
     * 目标端 kafka 配置
     */
    private List<EngineSinkKafkaModel> kafkaSinks = new ArrayList<>();

    /**
     * 目标端 clickhouse 配置
     */
    private List<EngineSinkClickhouseModel> clickhouseSinks = new ArrayList<>();

    /**
     * 目标端 hive-thrift 配置
     */
    private List<EngineSinkHiveModel> hiveSinks = new ArrayList<>();

    /**
     * 目标端 Hdfs-file 配置
     */
    private List<EngineSinkHdfsFileModel> hdfsFileSinks = new ArrayList<>();

    /**
     * 目标端mongodb配置
     */
    private List<EngineSinkMongoDBModel> mongodbSinks = new ArrayList<>();

    /**
     * 目标端kudu配置
     */
    private List<EngineSinkKuduModel> kuduSinks = new ArrayList<>();

    /**
     * 目标端localfile配置
     */
    private List<EngineSinkLocalFileModel> localFileSinks = new ArrayList<>();

    /**
     * 目标端sftp配置
     */
    private List<EngineSinkSFTPModel> sftpSinks = new ArrayList<>();

    private List<EngineSinkSFTPModel> mergeSftpSinks = new ArrayList<>();

    /**
     * 目标端ftp配置
     */
    private List<EngineSinkFTPModel> ftpSinks = new ArrayList<>();

    private List<EngineSinkFTPModel> mergeFtpSinks = new ArrayList<>();

    /**
     * 目标端阿里oss配置
     */
    private List<EngineSinkOssAliFileModel> ossAliSinks = new ArrayList<>();

    /**
     * 目标端s3配置
     */
    private List<EngineSinkS3FileModel> s3FileSinks = new ArrayList<>();

    /**
     * 目标端华为oss配置
     */
    private List<EngineSinkOssHuaweiFileModel> ossHuaweiSinks = new ArrayList<>();

    private List<EngineSinkDorisModel> dorisSinks = new ArrayList<>();

    private List<EngineSinkConsoleHoleModel> consoleHoleSinks = new ArrayList<>();

    private List<EngineSinkDataHubModel> dataHubSinks = new ArrayList<>();

    //需要合并文件的写入
    private List<EngineSinkLocalFileModel> mergeLocalFileSinks = new ArrayList<>();
    //argo HDFS FILE配置
    private List<EngineSinkArgoHdfsFileModel> argoHdfsFileSinks = new ArrayList<>();

    /**
     * 目标端elasticsearch配置
     */
    private List<EngineSinkElasticsearchModel> elasticsearchSinks = new ArrayList<>();

    /**
     * 目标端hbase配置
     */
    private List<EngineSinkHbaseModel> hbaseSinks = new ArrayList<>();

    /**
     * 目标端端localfile2file配置
     */
    private List<EngineSinkLocalFile2FileModel> localFile2FileSinks = new ArrayList<>();

    /**
     * 目标端端OssAliFile2File配置
     */
    private List<EngineSinkOssAliFile2FileModel> ossAliFile2FileSinks = new ArrayList<>();

    /**
     * 目标端端s3File2File配置
     */
    private List<EngineSinkS3File2FileModel> s3File2FileSinks = new ArrayList<>();

    /**
     * 目标端端sftpFile2File配置
     */
    private List<EngineSinkSFTP2FileModel> sftpFile2FileSinks = new ArrayList<>();

    /**
     * 目标端端inspurOSSFile2File配置
     */
    private List<EngineSinkInspurOSSFile2FileModel> inspurOSSFile2FileSinks = new ArrayList<>();

    /**
     * 目标端adbGpdist配置
     */
    private List<EngineSinkAdbGpdistModel> adbGpdistSinks = new ArrayList<>();

    /**
     * 目标端dws_gds
     */
    private List<EngineSinkDWSPGModel> dwspgSinks = new ArrayList<>();

    /**
     * 目标端 http sink组件配置
     */

    private List<EngineHttpBaseSinkModel> httpBaseSinks = new ArrayList<>();


    /**
     * 目标端 maxCompute sink组件配置
     */

    private List<EngineMaxComputeBaseSinkModel> maxComputeSinks = new ArrayList<>();

    /**
     * 使用FTL文件,固定值
     */
    private String ftlName = "engine.ftl";
    /**
     * 任务编号,由调用方生成,不可重复
     */
    //private String number;
    /**
     * 任务批次,由调用方生成,不可重复,后续所有接口需使用此字段
     */
    //private String batchNo;

}
