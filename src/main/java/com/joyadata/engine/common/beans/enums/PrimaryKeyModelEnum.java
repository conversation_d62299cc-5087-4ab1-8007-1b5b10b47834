package com.joyadata.engine.common.beans.enums;

public enum PrimaryKeyModelEnum {
    UNIQUE("UNIQUE","主键唯一模型"),
    DUPLICATE("DUPLICATE","主键重复模型");

    private String name;
    private String desc;

    PrimaryKeyModelEnum() {
    }

    public static PrimaryKeyModelEnum getPrimaryKeyModelEnum(String name) {
        PrimaryKeyModelEnum[] primaryKeyModelEnums = PrimaryKeyModelEnum.values();
        for (PrimaryKeyModelEnum primaryKeyModelEnum : primaryKeyModelEnums) {
            if (primaryKeyModelEnum.name.equalsIgnoreCase(name)) {
                return primaryKeyModelEnum;
            }
        }
        return null;
    }

    PrimaryKeyModelEnum(String name, String desc) {
        this.name = name;
        this.desc = desc;
    }

    public String getName() {
        return name;
    }

    public String getDesc() {
        return desc;
    }
}
