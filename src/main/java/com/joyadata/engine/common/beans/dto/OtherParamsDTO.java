package com.joyadata.engine.common.beans.dto;

import com.joyadata.engine.common.beans.enums.ProductSourceEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version V1.0
 * @ClassName: OtherParams
 * @date 2024/10/17
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class OtherParamsDTO {
    /**
     * 用来区分哪个产品来的数据，默认0:集成开发;1:数据资产
     */
    private ProductSourceEnum productSource = ProductSourceEnum.INTEGRATION;

    /**
     * 用户id
     */
    private String userId;
}
