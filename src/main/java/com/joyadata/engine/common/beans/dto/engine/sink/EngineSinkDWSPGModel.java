package com.joyadata.engine.common.beans.dto.engine.sink;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class EngineSinkDWSPGModel {
    private String path;

    private String tmpPath;

    private String fileFormatType = "csv";

    private String fieldDelimiter = ",";

    private String rowDelimiter = "\\n";

    private String finalName;

    private String batchSize = "10000000000";

    private String dwsUrl;

    private String dwsDriver;

    private String dwsUser;

    private String dwsPassword;

    private String dwsTable;

    private String dwsSchema;

    private String dwsGdsAddress;

    private String dwsFields;

    private String dwsCleanCache = "true";
}
