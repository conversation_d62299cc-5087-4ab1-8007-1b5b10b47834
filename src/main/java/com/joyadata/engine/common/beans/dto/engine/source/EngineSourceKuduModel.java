package com.joyadata.engine.common.beans.dto.engine.source;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.lang.reflect.Array;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/3/14
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class EngineSourceKuduModel extends EngineBaseSourceModel implements Serializable {
    /**
     * 如果未指定`result_table_name`，则该插件处理的数据不会注册为可被其他插件直接访问的数据集`(dataStream/dataset)`，
     * 也称为临时表`(table)`；如果指定了`result_table_name`，则该插件处理的数据会注册为可被其他插件直接访问的数据集`(dataStream/dataset)`，
     * 也称为临时表`(table)`。这里注册的数据集`(dataStream/dataset)`，其他插件通过指定`source_table_name`可直接进行访问。
     * https://seatunnel.apache.org/docs/2.3.3/connector-v2/source/common-options
     */
    private String resultTableName;
    /**
     * Kudu master address. Separated by ',',such as '**************:7051'.
     */
    private String kuduMasters;
    /**
     * 表名
     */
    private String tableName;
    /**
     * 列集合
     */
    private String columnsList;
    /**
     * Kudu worker count. Default value is twice the current number of cpu cores. 默认值：2 * Runtime.getRuntime().availableProcessors()
     */
    private int clientWorkerCount;

    /**
     * Kudu normal operation time out. 默认值：30000
     */
    private Long clientDefaultOperationTimeoutMs;

    /**
     * Kudu admin operation time out. 默认值：30000
     */
    private Long clientDefaultAdminOperationTimeoutMs;

    /**
     * Kerberos principal enable. 默认值：false
     */
    private String enableKerberos;

    /**
     * Kerberos principal. Note that all zeta nodes require have this file.
     */
    private String kerberosPrincipal;

    /**
     * Kerberos keytab. Note that all zeta nodes require have this file.
     */
    private String kerberosKeytab;

    /**
     * Kerberos krb5 conf. Note that all zeta nodes require have this file.
     */
    private String kerberosKrb5conf;

    /**
     * The timeout for connecting scan token. If not set, it will be the same as operationTimeout. 默认值：30000
     */
    private Long scanTokenQueryTimeout;

    /**
     * Kudu scan bytes. The maximum number of bytes read at a time, the default is 1MB. 默认值：1024 * 1024
     */
    private int scanTokenBatchSizeBytes;

    /**
     * SeaTunnel Schema.
     */
    private LinkedHashMap schema;

    /**
     * The list of tables to be read. you can use this configuration instead of `table_path` example: ```table_list = [{ table_name = "kudu_source_table_1"},{ table_name = "kudu_source_table_2"}]
     */
    private Array tableList;
}
