package com.joyadata.engine.common.beans.enums;

/**
 * <AUTHOR>
 * @version V1.0
 * @ClassName: HiveStorageTypeEnum
 * @date 2024/10/23
 */
public enum HiveStorageTypeEnum {

    PARQUET("parquet"),
    ORC("orc"),
    TEXTFILE("textfile");

    private String name;

    HiveStorageTypeEnum() {
    }

    HiveStorageTypeEnum(String name) {
        this.name = name;
    }

    public static HiveStorageTypeEnum getHiveStorageTypeEnum(String name) {
        HiveStorageTypeEnum[] hiveStorageTypeEnums = HiveStorageTypeEnum.values();
        for (HiveStorageTypeEnum hiveStorageTypeEnum : hiveStorageTypeEnums) {
            if (hiveStorageTypeEnum.name.equalsIgnoreCase(name)) {
                return hiveStorageTypeEnum;
            }
        }
        return null;
    }

    public String getName() {
        return name;
    }
}
