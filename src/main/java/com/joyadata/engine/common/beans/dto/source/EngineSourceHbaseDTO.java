package com.joyadata.engine.common.beans.dto.source;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.LinkedHashMap;
import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2024/9/26 11:01.
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@EqualsAndHashCode(callSuper = true)
@Data
public class EngineSourceHbaseDTO extends EngineBaseSourceDTO {
    /**
     * 表名
     */
    private String tableName;

    /**
     * query_columns
     */
    private List<String> queryColumns;

    /**
     * schema
     */
    private LinkedHashMap<String, String> schema;
}
