package com.joyadata.engine.common.beans.dto.engine.sink;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/6/11
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class EngineSinkDorisModel extends EngineBaseSinkModel implements Serializable {
    /**
     * ip:port，多个逗号隔开
     */
    private String fenodes;
    /**
     * 端口，默认9030
     */
    private int queryPort;

    /**
     * 账号
     */
    private String username;

    /**
     * 密码
     */
    private String password;

    /**
     * 数据库
     */
    private String database;

    /**
     * 表
     */
    private String table;

    /**
     * 表 在2.3.5会被弃用
     */
    private String tableIdentifier;

    /**
     * 流加载导入所使用的标签前缀。在2pc场景中，需要全局唯一性来确保SeaTunnel的EOS语义
     */
    private String sinkLabelPrefix;

    /**
     * 是否启用两阶段提交（2pc），默认为true，以确保精确一次语义
     */
    private String sinkEnable2pc = "true";

    /**
     * 是否启用删除。此选项要求Doris表启用批量删除功能（默认启用0.15+版本），并且仅支持Unique模型。
     */
    private String sinkEnableDelete = "false";

    /**
     * 加载时使用间隔检查异常,默认 10000
     */
    private int sinkCheckInterval;

    /**
     * 将记录写入数据库失败时的最大重试次数
     */
    private int sinkMaxRetries;
    /**
     * 用于缓存流加载数据的缓冲区大小，默认 256 * 1024
     */
    private int sinkBufferSize;

    /**
     * 缓存数据以进行流加载的缓冲区计数 默认3
     */
    private int sinkBufferCount;

    /**
     * 写入doris的批量大小是每个http请求，当行达到该大小或执行检查点时，缓存的数据将写入服务器。默认1024
     */
    private int dorisBatchSize;

    /**
     * 是否启用不支持的类型转换，如Decimal64到Double  默认false
     */
    private String needsUnsupportedTypeCasting;

    /**
     * 数据保存模式 DROP_DATA,APPEND_DATA,CUSTOM_PROCESSING,ERROR_WHEN_DATA_EXISTS。默认APPEND_DATA
     */
    private String dataSaveMode;

    /**
     * 当data_save_mode选择CUSTOM_PROCESSING时，应填写CUSTOM_SQL参数。此参数通常填充可以执行的SQL。SQL将在执行同步任务之前执行
     */
    private String customSql;

    /**
     * 此选项用于在自动生成sql和支持的格式时支持插入、删除和更新等操作
     */
    private Map<String, String> dorisConfig;
}
