package com.joyadata.engine.common.beans.dto.engine.source;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/6/11
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class EngineSourceDorisModel implements Serializable {
    private String resultTableName;

    /**
     * 请求地址,ip:port
     */
    private String fenodes;

    /**
     * 账号
     */
    private String username;
    /**
     * 密码
     */
    private String password;

    /**
     * database
     */
    private String database;

    /**
     * 表名
     */
    private String table;

    /**
     * 使用“doris.read.field”参数选择要读取的doris表列
     */
    private String dorisReadField;

    /**
     * 查询端口
     */
    private String queryPort;

    /**
     * 数据筛选 例如id>2
     */
    private String dorisFilterQuery;

    /**
     * 通过一次读取Doris be可以获得的最大值。默认 1024
     */
    private String dorisBatchSize;

    /**
     * Doris扫描数据的超时时间，以秒为单位。默认(3600)
     */
    private String dorisRequestQueryTimeouts;

    /**
     * 单个请求最大内存 默认2G（2147483648）
     */
    private long dorisExecMemLimit;

    /**
     * 重试次数
     */
    private int dorisRequestRetries;

    /**
     * 读数据请求超时，单位毫秒 ，默认30000
     */
    private int dorisRequestReadTimeoutMs;

    /**
     * 连接超时时间，单位毫秒，默认30000
     */
    private int dorisRequestConnectTimeoutMs;

    // source的并行度
    private int parallelism;
}
