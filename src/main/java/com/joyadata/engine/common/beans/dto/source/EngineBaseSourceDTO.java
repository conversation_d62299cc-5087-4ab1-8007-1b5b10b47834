package com.joyadata.engine.common.beans.dto.source;


import com.joyadata.engine.common.beans.enums.EngineSourceTypeEnum;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version V1.0
 * @ClassName: EngineBaseSourceDTO
 * @date 2024/2/21
 */
@Data
public class EngineBaseSourceDTO implements Serializable {

    /**
     * 数据源ID
     */
    private String datasourceInfoId;

    /**
     * 并发度
     * 当未指定`parallelism`时，默认使用`env`中的`parallelism`值；当指定`parallelism`时，将覆盖`env`中的`parallelism`值
     */
    private String parallelism;

}
