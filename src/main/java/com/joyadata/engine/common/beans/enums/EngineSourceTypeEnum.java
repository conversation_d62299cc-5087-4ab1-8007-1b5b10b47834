package com.joyadata.engine.common.beans.enums;

/**
 * <AUTHOR>
 * @version V1.0
 * @ClassName: EngineModelType
 * @date 2024/2/21
 */
public enum EngineSourceTypeEnum {
    /**
     * source
     */
    JDBC,
    ORACLECDC,
    MYSQLCDC,
    SQLSERVERCDC,
    POSTGRESCDC,
    KAFKA,
    ELASTICSEARCH,
    MONGODB,
    K<PERSON>DU,
    HD<PERSON><PERSON><PERSON>,
    L<PERSON>AL<PERSON><PERSON>,
    SFTP<PERSON>LE,
    FTP<PERSON>LE,
    OSS_ALI,
    S3<PERSON><PERSON>,
    DORIS,
    OSS_HUAWEI,
    DATA_HUB,
    ADB_GPDIST,
    CLICKHOUSE,
    HBASE,
    LOCAL<PERSON><PERSON>2<PERSON><PERSON>,
    OSS_ALI2FILE,
    S3<PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    INSPUROSSF<PERSON>E<PERSON><PERSON><PERSON>,
    ARG<PERSON>_HDFS_FILE,
    HTTP,
    MAXCOMPUT<PERSON>
}
