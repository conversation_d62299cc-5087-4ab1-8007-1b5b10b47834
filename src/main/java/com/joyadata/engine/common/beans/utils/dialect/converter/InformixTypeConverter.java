package com.joyadata.engine.common.beans.utils.dialect.converter;

import com.joyadata.engine.common.beans.dto.DdlDTO;
import com.joyadata.engine.common.beans.dto.datasource.MetadataColumnDTO;
import com.joyadata.engine.common.beans.utils.dialect.AbstractDatabaseTypeConverter;
import com.joyadata.engine.common.beans.utils.dialect.paramters.DDLParameters;
import groovy.lang.Tuple2;
import groovy.lang.Tuple3;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.sql.Types;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2025/4/27 17:50
 */
@Slf4j
public class InformixTypeConverter extends AbstractDatabaseTypeConverter {
    @Override
    protected Tuple3<String, String, String> doGenerateDDL(DDLParameters ddlParameters) {
        String schema = ddlParameters.getSchema();
        String tableName = ddlParameters.getTableName();
        List<DdlDTO> columns = ddlParameters.getColumns();
        String tableCnName = ddlParameters.getTableCnName();
        String fieldUppLower = ddlParameters.getFieldUppLower();
        if (StringUtils.isNotBlank(fieldUppLower)) {
            tableName = getUppOrLowerCase(fieldUppLower, tableName);
        }
        String checkTableSql = "select 1 from " + tableName + " where 1=2";
        String create = "create table " + tableName + " (";
        if (StringUtils.isNotBlank(schema)) {
            if (StringUtils.isNotBlank(fieldUppLower)) {
                schema = getUppOrLowerCase(fieldUppLower, schema);
            }
            create = "create table " + schema + "." + tableName + " (";
            checkTableSql = "select 1 from " + schema + "." + tableName + " where 1=2";
        }
        StringBuilder ddl = new StringBuilder(create);
        columns.forEach(ddlDTO -> {
            int dataType = ddlDTO.getDataTypeJava();// Integer.valueOf(column.get("data_type"));
            int columnSize = ddlDTO.getColumnSize();// Integer.valueOf(column.get("max_length"));
            int decimalDigits = ddlDTO.getDecimalDigits(); //Integer.valueOf(column.get("decimal_digits"));
            String dataName = ddlDTO.getDataType(); //column.get("type_name");
            String columnName;
            if (StringUtils.isNotBlank(fieldUppLower)) {
                columnName = getUppOrLowerCase(fieldUppLower, ddlDTO.getColumnName());
            } else {
                columnName = ddlDTO.getColumnName().toLowerCase();
            }
            String dataTypeString = getInforMixTypeFromSqlType(dataType, dataName, columnSize, decimalDigits);
            ddl.append(columnName).append(" ").append(dataTypeString).append(",");
        });
        String result = ddl.toString();
        result = result.substring(0, result.length() - 1);
        //表中文名
        result = result + ")";
        log.info("InformixDDLFromMetadata生成Informix建表语句是 {}", result);
        log.info("InformixDDLFromMetadata生成Informix检测表是否存在的语句是 {}", checkTableSql);
        Tuple3<String, String, String> tuple = new Tuple3(result, "", checkTableSql);//mysql 备注在sql中
        return tuple;
    }

    public static String getInforMixTypeFromSqlType(int dataType, String dataName, int columnSize, int decimalDigits) {
        switch (dataType) {
            case Types.BIT:
            case Types.INTEGER:
                return "INT";
            case Types.BOOLEAN:
                return "BOOLEAN";
            case Types.TINYINT:
                return "INTEGER";
            case Types.SMALLINT:
                return "SMALLINT";
            case Types.BIGINT:
                return "BIGINT";
            case Types.FLOAT:
                return "FLOAT";
            case Types.REAL:
                return "REAL";
            case Types.DOUBLE:
                return "DOUBLE PRECISION";
            case Types.NUMERIC:
            case Types.DECIMAL:
                if (decimalDigits == 0) {
                    if (columnSize == 1) {
                        return "INTEGER";
                    } else if (columnSize < 3) {
                        return "SMALLINT";
                    } else if (columnSize < 10) {
                        return "INT";
                    } else if (columnSize < 19) {
                        return "BIGINT";
                    } else if (columnSize > 32) {
                        return "DECIMAL(32,0)";
                    } else {
                        return "DECIMAL(" + columnSize + ",0)";
                    }
                } else if (decimalDigits == -127) {
                    if (columnSize == 0 || columnSize > 32) {
                        return "DECIMAL(" + 32 + ", " + 0 + ")";
                    } else {
                        return "DECIMAL(" + columnSize + ", " + 0 + ")";
                    }
                } else {
                    if ("float4".equalsIgnoreCase(dataName) || "float8".equalsIgnoreCase(dataName)) {//pg字段处理
                        return "DOUBLE PRECISION";
                    }
                    return "DECIMAL(" + columnSize + ", " + decimalDigits + ")";
                }
            case Types.DATE:
                return "DATE";
            case Types.TIMESTAMP:
                return "DATETIME YEAR TO SECOND";
            case Types.BINARY:
            case Types.VARBINARY:
            case Types.LONGVARBINARY:
            case Types.BLOB:
                return "BYTE";
            case Types.CHAR:
            case Types.VARCHAR:
            case Types.NCHAR:
            case Types.NVARCHAR:
            case Types.LONGNVARCHAR:
            case Types.LONGVARCHAR:
                if (columnSize > 0 && columnSize <= 255) {
                    return "VARCHAR(" + columnSize + ")";
                } else {
                    return "TEXT";
                }
            case Types.CLOB:
                return "TEXT";
            case Types.NCLOB:
                return "CLOB";
            default:
                if (StringUtils.isNotBlank(dataName)) {
                    if ("NVARCHAR2".equalsIgnoreCase(dataName)) {
                        return "VARCHAR(" + columnSize + ")";
                    } else if ("VARCHAR2".equalsIgnoreCase(dataName)) {
                        return "VARCHAR(" + columnSize + ")";
                    } else if (dataName.startsWith("TIMESTAMP")) {
                        return "DATETIME YEAR TO SECOND";
                    } else if ("INTERVAL YEAR(2) TO MONTH".equalsIgnoreCase(dataName)) {
                        return "VARCHAR(10)";
                    } else if (dataName.startsWith("TIMESTAMP")) {
                        return "DATETIME";
                    } else if (dataName.equalsIgnoreCase("serial")) {//pg数据库转int
                        return "INT";
                    }
                }
                return "TEXT";
        }
    }

    @Override
    public Tuple2<String, List<String>> generateAddPkSql(String schema, String tableName, List<MetadataColumnDTO> fieldList, String fieldUppLower) {
        List<String> pkFieldList = fieldList.stream().map(MetadataColumnDTO::getColumnName).collect(Collectors.toList());
        if (StringUtils.isNotBlank(fieldUppLower)) {
            tableName = getUppOrLowerCase(fieldUppLower, tableName);
            pkFieldList = convertListCase(pkFieldList, fieldUppLower);
        }
        //ALTER TABLE postgres.test222 ADD CONSTRAINT test222_pk PRIMARY KEY (id);
        String pkSql = "ALTER  TABLE  " + tableName + " ADD CONSTRAINT " + " PRIMARY KEY (" + String.join(",", pkFieldList) + ")" + " constraint pk_" + System.currentTimeMillis();
        if (StringUtils.isNotBlank(schema)) {
            if (StringUtils.isNotBlank(fieldUppLower)) {
                schema = getUppOrLowerCase(fieldUppLower, schema);
            }
            pkSql = "ALTER  TABLE " + schema + "." + tableName + " ADD CONSTRAINT " + " PRIMARY KEY (" + String.join(",", pkFieldList) + ")" + " constraint pk_" + System.currentTimeMillis();
        }
        Tuple2<String, List<String>> tuple2 = new Tuple2<>(pkSql, null);
        return tuple2;
    }
}
