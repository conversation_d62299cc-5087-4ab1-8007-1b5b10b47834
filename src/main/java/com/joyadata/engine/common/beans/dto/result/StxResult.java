package com.joyadata.engine.common.beans.dto.result;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * stx返回参数
 *
 * <AUTHOR>
 * @date 2024/7/30
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class StxResult {

    /**
     * 任务code
     */
    private String stxTaskCode;
    /**
     * 任务名称
     */
    private String stxTaskName;
    /**
     * 任务实例id
     */
    private String stxTaskInstanceId;
    /**
     * 0 成功、1失败
     */
    private int stxCode = 1;

    /**
     * 读总条数
     */
    private String stxTotalReadCount = "0";

    /**
     * 写总条数
     */
    private String stxTotalWriteCount = "0";

    /**
     * 任务总耗时，单位秒
     */
    private String stxTotalTimeSeconds = "0";

    /**
     * 开始时间
     */
    private String stxStartTime = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());

    /**
     * 结束时间
     */
    private String stxEndTime = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
}
