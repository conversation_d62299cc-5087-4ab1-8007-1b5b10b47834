package com.joyadata.engine.common.beans.dto.sink;

import com.joyadata.engine.common.beans.dto.datasource.MetadataColumnDTO;
import com.joyadata.engine.common.beans.enums.SinkPreDdlEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class EngineSinkDWSPGDTO {
    /**
     * 数据源id
     */
    private String datasourceInfoId;

    private String path;

    private String tableName;

    private String gdsAddress;

    /**
     * 传输列
     */
    private List<MetadataColumnDTO> fieldList;

    /**
     * NONE 不做处理
     * DROP 修改表名称为:DROP_YYYMMDDHHMMSS_原表名称
     * TRUNACATE 修改表名称为:TRUNACATE_YYYMMDDHHMMSS_原表名称
     */
    private SinkPreDdlEnum sinkPreDdl;

    /**
     * 任务是否建表
     */
    private Boolean createAlterTable;

    /**
     * 库名
     */
    private String dbName;
    /**
     * 模式名
     */
    private String schemaName;
    /**
     * null，none、upper、lower
     */
    private String fieldUppLow;
}
