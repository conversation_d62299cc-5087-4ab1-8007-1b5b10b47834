package com.joyadata.engine.common.beans.dto.engine.source;


import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/3/18
 */
@Data
public class EngineBaseSourceModel implements Serializable {

    /**
     * 如果未指定`result_table_name`，则该插件处理的数据不会注册为可被其他插件直接访问的数据集`(dataStream/dataset)`，
     * 也称为临时表`(table)`；如果指定了`result_table_name`，则该插件处理的数据会注册为可被其他插件直接访问的数据集`(dataStream/dataset)`，
     * 也称为临时表`(table)`。这里注册的数据集`(dataStream/dataset)`，其他插件通过指定`source_table_name`可直接进行访问。
     * https://seatunnel.apache.org/docs/2.3.3/connector-v2/source/common-options
     */
    private String resultTableName;
    /**
     * 并发度
     * 当未指定`parallelism`时，默认使用`env`中的`parallelism`值；当指定`parallelism`时，将覆盖`env`中的`parallelism`值
     */
    private String parallelism;

}
