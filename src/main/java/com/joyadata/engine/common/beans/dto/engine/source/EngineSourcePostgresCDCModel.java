package com.joyadata.engine.common.beans.dto.engine.source;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/2/21
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class EngineSourcePostgresCDCModel implements Serializable {
    /**
     * JDBC连接基础URL
     */
    private String baseUrl;

    /**
     * 数据库用户名
     */
    private String username;

    /**
     * 数据库密码
     */
    private String password;

    /**
     * 数据库名称列表 多个用逗号分割。
     */
    private String databaseNames;

    /**
     * 模式名称列表 多个用逗号分割。
     */
    private String schemaNames;

    /**
     * 表名称列表 多个用逗号分割。
     */
    private String tableNames;

    /**
     * 结果表名称
     */
    private String resultTableName;

    /**
     * Debezium配置
     */
    private Map<String, String> debezium;


}
