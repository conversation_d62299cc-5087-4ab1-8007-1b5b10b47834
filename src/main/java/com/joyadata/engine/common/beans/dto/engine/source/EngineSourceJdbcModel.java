package com.joyadata.engine.common.beans.dto.engine.source;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/2/21
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class EngineSourceJdbcModel implements Serializable {
    /**
     * 源端jdbcurl信息
     */
    private String url;
    /**
     * 源端jdbc 驱动信息
     */
    private String driver;
    /**
     * 源端jdbc 账号
     */
    private String user;
    /**
     * 源端jdbc 密码
     */
    private String password;
    /**
     * 查询语句  根据输入表自定义选择内容后拼接的sql语句，例如select id,nam from user where id>10
     */
    private String query;
    /**
     * 对应页面的schemaName
     */
    private String schemaName;
    /**
     * 等待数据库操作完成以验证连接的时间(秒)
     */
    private String connectionCheckTimeoutSec = "30";
    /**
     * 并行分区的列名，仅支持数字类型。
     * 并行读取拆分使用的字段
     */
    private String partitionColumn;

    //分片数
    /**
     * 当设置分片数后，partitionColumn必须设置
     */
    private String partitionNum;

    /**
     * 如果未指定`result_table_name`，则该插件处理的数据不会注册为可被其他插件直接访问的数据集`(dataStream/dataset)`，
     * 也称为临时表`(table)`；如果指定了`result_table_name`，则该插件处理的数据会注册为可被其他插件直接访问的数据集`(dataStream/dataset)`，
     * 也称为临时表`(table)`。这里注册的数据集`(dataStream/dataset)`，其他插件通过指定`source_table_name`可直接进行访问。
     * https://seatunnel.apache.org/docs/2.3.3/connector-v2/source/common-options
     */
    private String resultTableName;
    /**
     * 并发度
     * 当未指定`parallelism`时，默认使用`env`中的`parallelism`值；当指定`parallelism`时，将覆盖`env`中的`parallelism`值
     */
    private String parallelism;
    /**
     * 表快照的分割大小（行数），读取表快照时，捕获的表被分割为多个分割
     */
    private String snapshotSplitSize = "8096";
    /**
     * 读取表快照时每次轮询的最大提取大小
     */
    private String snapshotFetchSize = "1024";
    /**
     * 连接器在超时之前尝试连接到数据库服务器之后应该等待的最大时间。
     * JDBC连接超时时长（豪秒）
     */
    private String connectTimeoutMs = "30000";
    /**
     * 连接器应该重试建立数据库服务器连接的最大重试次数
     */
    private String connectMaxRetries = "3";
    /**
     * 批量条数
     */
    private String batchSize = "1024";
    /**
     * 连接池的大小
     */
    private String connectionPoolSize = "20";
    /**
     * 块键分布因子的下界。分布因子用于确定表是否均匀分布。当数据分布均匀时，表块将使用均匀计算优化，当数据分布不均匀时，会发生查询拆分。
     * 分布因子可以通过（MAX（id）- MIN（id）+1）/ rowCount计算得出
     */
    private String chunkKeyEvenDistributionFactorLowerBound = "0.05";
    /**
     * 块键分布因子的上界。分布因子用于确定表是否均匀分布。当数据分布均匀时，表块将使用均匀计算优化，当数据分布不均匀时，会发生查询拆分。
     * 分布因子可以通过（MAX（id）- MIN（id）+1）/ rowCount计算得出
     */
    private String chunkKeyEvenDistributionFactorUpperBound = "100";
    /**
     * 这是指触发样本分片策略的估计分片数量的阈值。当分布因子超出上下界时，如果估计的分片数量（approximateRowCnt/chunkSize）超过此阈值，
     * 将使用样本分片策略。这种策略可以帮助更有效地处理大型数据集。默认值为1000个分片。
     */
    private String sampleShardingThreshold = "1000";
    /**
     * 样本分片策略的抽样率的倒数。该值表示抽样率分数的分母。例如，值为1000表示抽样率为1/1000。当触发样本分片策略时使用此参数。
     */
    private String inverseSamplingRate = "1000";
    /**
     * CDC源的可选启动模式，有效的枚举是“initial”，“earliest”，“latest”，“timestamp”或“specific”
     */
    private String startupMode = "INITIAL";
    /**
     * CDC源的可选停止模式，有效的枚举是“never”，“latest”，“timestamp”或“specific”
     */
    private String stopMode = "NEVER";


    private String tablePath;

    /**
     * oceanBase用来区分mysql内核和oracle内核，mysql不填，oracle填oracle
     */
    private String compatibleMode;


    //hive的认证需要的认证文件路径参数
    /**
     * hdfs-site.xml的路径，用于加载名称节点的ha配置
     */
    private String hdfsSitePath;

    /**
     * hive-site.xml的路径，用于验证配置单元元存储
     */
    private String hiveSitePath;

    /**
     * 是否开启Kerberos认证
     */
    private String useKerberos;

    /**
     * kerberos的krb5路径
     */
    private String krb5Path;

    /**
     * The principal of kerberos
     */
    private String kerberosPrincipal;

    /**
     * kerberos的keytab路径
     */
    private String kerberosKeytabPath;
    /**
     * 空值处理
     */
    private String emptyDataStrategy;

    /**
     * 通道数，对应engine.ftl中的key table_path
     */
    private Integer pipeline;

}
