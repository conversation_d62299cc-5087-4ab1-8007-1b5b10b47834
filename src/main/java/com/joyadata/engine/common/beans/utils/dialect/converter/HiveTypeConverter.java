package com.joyadata.engine.common.beans.utils.dialect.converter;

import com.joyadata.engine.common.beans.dto.DdlDTO;
import com.joyadata.engine.common.beans.dto.datasource.MetadataColumnDTO;
import com.joyadata.engine.common.beans.enums.HiveStorageTypeEnum;
import com.joyadata.engine.common.beans.utils.dialect.AbstractDatabaseTypeConverter;
import com.joyadata.engine.common.beans.utils.dialect.paramters.DDLParameters;
import com.joyadata.engine.common.beans.utils.dialect.paramters.HiveDDLParameters;
import groovy.lang.Tuple2;
import groovy.lang.Tuple3;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.sql.Types;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @Date 2025/4/27 17:50
 */
@Slf4j
public class HiveTypeConverter extends AbstractDatabaseTypeConverter {
    @Override
    protected Tuple3<String, String, String> doGenerateDDL(DDLParameters ddlParameters) {
        HiveDDLParameters hiveDDLParameters = (HiveDDLParameters) ddlParameters;
        String schema = hiveDDLParameters.getSchema();
        String tableName = hiveDDLParameters.getTableName();
        List<DdlDTO> columns = hiveDDLParameters.getColumns();
        String tableCnName = hiveDDLParameters.getTableCnName();
        String fieldUppLower = hiveDDLParameters.getFieldUppLower();
        HiveStorageTypeEnum hiveStorageType = hiveDDLParameters.getHiveStorageType();
        if (StringUtils.isNotBlank(fieldUppLower)) {
            tableName = getUppOrLowerCase(fieldUppLower, tableName);
        }
        String create = "create table " + tableName + " (";
        String checkTable = "select 1 from " + tableName + " where 1=2";
        if (StringUtils.isNotBlank(schema)) {
            if (StringUtils.isNotBlank(fieldUppLower)) {
                schema = getUppOrLowerCase(fieldUppLower, schema);
            }
            create = "create table " + schema + "." + tableName + " (";
            checkTable = "select 1 from " + schema + "." + tableName + " where 1=2";
        }
        StringBuilder partitionSql = new StringBuilder();
        Optional<DdlDTO> incrFieldCol = columns.stream().filter(t -> t.getPartition() == 1).findFirst();
        if (incrFieldCol.isPresent()) {
            partitionSql.append("PARTITIONED BY (");
        }
        //2024-12-26  hive类型建表。如果是parquet，所有的DATE类型转为TIMESTAMP
        if (HiveStorageTypeEnum.PARQUET.equals(hiveStorageType)) {
            columns.forEach(t -> {
                if (Types.DATE == t.getDataTypeJava()) {
                    t.setDataTypeJava(Types.TIMESTAMP);
                }
            });
        }
        StringBuilder ddl = new StringBuilder(create);
        columns.forEach(ddlDTO -> {
            int dataType = ddlDTO.getDataTypeJava();// Integer.valueOf(column.get("data_type"));
            int columnSize = ddlDTO.getColumnSize();// Integer.valueOf(column.get("max_length"));
            int decimalDigits = ddlDTO.getDecimalDigits(); //Integer.valueOf(column.get("decimal_digits"));
            String dataName = ddlDTO.getDataType(); //column.get("type_name");
            String columnName;
            if (StringUtils.isNotBlank(fieldUppLower)) {
                columnName = getUppOrLowerCase(fieldUppLower, ddlDTO.getColumnName());
            } else {
                columnName = ddlDTO.getColumnName();
            }
            String cnName = ddlDTO.getColumnCnName();// StringUtils.defaultString(column.get("remarks"),"");
            String dataTypeString = getHiveTypeFromSqlType(dataType, dataName, columnSize, decimalDigits);
            //log.info("当前字段名称是 {} ,字段javaType是 {} ,元数据名称是 {},转换为 {}", columnName, dataType, dataName, dataTypeString);
            if (StringUtils.isNotBlank(cnName)) {
                cnName = cnName.replaceAll("'", "`");
                cnName = cnName.replaceAll("\"", "“");
                if (ddlDTO.getPartition() == 1) {
                    partitionSql.append("`").append(columnName).append("` ").append(dataTypeString).append(" COMMENT '").append(cnName).append("',");
                } else {
                    ddl.append("`").append(columnName).append("` ").append(dataTypeString).append(" COMMENT '").append(cnName).append("',");
                }
            } else {
                if (ddlDTO.getPartition() == 1) {
                    partitionSql.append("`").append(columnName).append("` ").append(dataTypeString).append(",");
                } else {
                    ddl.append("`").append(columnName).append("` ").append(dataTypeString).append(",");
                }
            }
        });
        String result = ddl.toString();
        result = result.substring(0, result.length() - 1);
        result = result + ")";
        //分区字段sql不为空，在拼接
        if (StringUtils.isNotBlank(partitionSql)) {
            result = result + (partitionSql.substring(0, partitionSql.length() - 1) + ")");
        }
        //储存格式，默认textfile
        if (null != hiveStorageType) {
            result = result + " stored as " + hiveStorageType.getName();
        } else {
            result = result + " stored as " + HiveStorageTypeEnum.ORC.getName();
        }
        //表中文名
        if (StringUtils.isNotBlank(tableCnName)) {
            tableCnName = tableCnName.replaceAll("'", "`");
            tableCnName = tableCnName.replaceAll("\"", "“");
            result = result + " COMMENT '" + tableCnName + "'";
        }
        log.info("HiveMetadata生成Hive建表语句是 {}", result);
        log.info("HiveMetadata生成Hive检测表是否存在的语句是 {}", checkTable);
        Tuple3<String, String, String> tuple3 = new Tuple3<>(result, "", checkTable);//字段注释和表注释在sql中
        return tuple3;
    }

    private static String getHiveTypeFromSqlType(int dataType, String dataName, int columnSize, int decimalDigits) {
        switch (dataType) {
            case Types.TINYINT:
                return "TINYINT";
            case Types.BOOLEAN:
                return "BOOLEAN";
            case Types.BIT:
            case Types.SMALLINT:
                return "SMALLINT";
            case Types.INTEGER:
                return "INT";
            case Types.BIGINT:
                return "BIGINT";
            case Types.FLOAT:
                return "FLOAT";
            case Types.REAL:
            case Types.DOUBLE:
                return "DOUBLE";
            case Types.NUMERIC:
            case Types.DECIMAL:
                if (columnSize == 0 && decimalDigits == 0) {
                    //20250814工银瑞信，如果长度精度都是0，默认38,18
                    return "DECIMAL(38, 18)";
                } else if (decimalDigits == 0) {
                    //工银瑞信 20250514 原生hive是没有MEDIUMINT的，所以这里注释掉
                    /*if (columnSize < 3) {
                        return "SMALLINT";
                    } else if (columnSize < 5) {
                        return "MEDIUMINT";
                    } else */
                    if (columnSize < 10) {
                        return "INT";
                    } else if (columnSize < 19) {
                        return "BIGINT";
                    } else if (columnSize > 65) {
                        return "DECIMAL( 65 )";
                    } else {
                        return "DECIMAL(" + columnSize + ")";
                    }
                } else if (decimalDigits == -127) {
                    if (columnSize == 0 || columnSize > 65) {
                        return "DECIMAL(" + 65 + ", " + 0 + ")";
                    } else {
                        return "DECIMAL(" + columnSize + ", " + 0 + ")";
                    }
                } else {
                    if ("float4".equalsIgnoreCase(dataName) || "float8".equalsIgnoreCase(dataName)) {//pg字段处理
                        return "DOUBLE";
                    }
                    return "DECIMAL(" + columnSize + ", " + decimalDigits + ")";
                }
            case Types.TIME:
                return "STRING";
            case Types.DATE:
                return "DATE";
            case Types.TIMESTAMP:
                return "TIMESTAMP";
            case Types.BINARY:
            case Types.VARBINARY:
            case Types.LONGVARBINARY:
                return "STRING";
            case Types.CHAR:
            case Types.VARCHAR:
            case Types.NCHAR:
            case Types.NVARCHAR:
            case Types.LONGNVARCHAR:
            case Types.LONGVARCHAR:
                if (columnSize > 0 && columnSize <= 255) {
                    return "VARCHAR(" + columnSize + ")";
                } else {
                    return "STRING";
                }
            case Types.CLOB:
            case Types.NCLOB:
            case Types.BLOB:
                return "STRING";
            default:
                if (StringUtils.isNotBlank(dataName)) {
                    if ("NVARCHAR2".equalsIgnoreCase(dataName)) {
                        return "VARCHAR(" + columnSize + ")";
                    } else if ("VARCHAR2".equalsIgnoreCase(dataName)) {
                        return "VARCHAR(" + columnSize + ")";
                    } else if (dataName.startsWith("TIMESTAMP")) {
                        return "TIMESTAMP";
                    } else if ("INTERVAL YEAR(2) TO MONTH".equalsIgnoreCase(dataName)) {
                        return "VARCHAR(10)";
                    } else if (dataName.equalsIgnoreCase("serial")) {//pg数据库转int
                        return "INT";
                    }
                }
                return "STRING";
        }
    }

    @Override
    public Tuple2<String, List<String>> generateAddPkSql(String schema, String tableName, List<MetadataColumnDTO> pkFieldList, String fieldUppLower) {
        return new Tuple2<>("", null);
    }
}
